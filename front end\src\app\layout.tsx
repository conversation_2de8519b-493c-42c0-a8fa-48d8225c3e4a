import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { TonConnectProvider } from '@/contexts/TonConnectContext';
import { SettingsProvider } from '@/contexts/SettingsContext';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'TON Voucher Platform',
  description: 'Secure e-commerce platform for premium game voucher codes with TON cryptocurrency payments',
  keywords: ['TON', 'voucher', 'cryptocurrency', 'gaming', 'blockchain'],
  authors: [{ name: 'TON Voucher Platform' }],
  creator: 'TON Voucher Platform',
  publisher: 'TON Voucher Platform',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    title: 'TON Voucher Platform',
    description: 'Secure e-commerce platform for premium game voucher codes with TON cryptocurrency payments',
    siteName: 'TON Voucher Platform',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TON Voucher Platform',
    description: 'Secure e-commerce platform for premium game voucher codes with TON cryptocurrency payments',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#0ea5e9' },
    { media: '(prefers-color-scheme: dark)', color: '#0284c7' },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <head>
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* TON Connect manifest */}
        <link rel="manifest" href="/tonconnect-manifest.json" />
      </head>
      <body className={`${inter.className} h-full antialiased`}>
        <TonConnectProvider>
          <AuthProvider>
            <SettingsProvider>
              <div id="root" className="h-full">
                {children}
              </div>
            </SettingsProvider>

          {/* Portal for modals */}
          <div id="modal-root" />

          {/* Portal for toasts */}
          <div id="toast-root" />

          {/* Hidden container for TonConnect UI */}
          <div id="ton-connect-container" style={{ display: 'none' }} />

          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                style: {
                  background: '#10b981',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />
          </AuthProvider>
        </TonConnectProvider>
      </body>
    </html>
  );
}
