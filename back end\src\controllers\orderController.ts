import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logPaymentEvent } from '../config/logger';
import { generateOrderId, sanitizeString } from '../utils/helpers';
import { sendEmail } from '../services/emailService';
import { generatePaymentAddress } from '../services/tonService';
import { settingsService } from '../services/settingsService';

// Create new order
export const createOrder = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { amount, currency = 'TON', memo, productId, quantity = 1 } = req.body;

    // Validate amount
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount',
      });
    }

    // Check business rules from system settings
    const paymentSettings = await settingsService.getPaymentSettings();
    const minAmount = paymentSettings.minOrderAmount;
    const maxAmount = paymentSettings.maxOrderAmount;

    if (numAmount < minAmount) {
      return res.status(400).json({
        success: false,
        error: `Minimum order amount is ${minAmount} ${currency}`,
      });
    }

    if (numAmount > maxAmount) {
      return res.status(400).json({
        success: false,
        error: `Maximum order amount is ${maxAmount} ${currency}`,
      });
    }

    // Validate product if provided
    let productData = null;
    if (productId) {
      const productResult = await executeQuery(
        'SELECT * FROM products WHERE id = $1 AND available = true',
        [productId]
      );

      if (productResult.rows.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Product not found or unavailable',
        });
      }

      productData = productResult.rows[0];

      // Validate amount against product constraints
      const minAmount = parseFloat(productData.min_amount);
      const maxAmount = parseFloat(productData.max_amount);

      if (numAmount < minAmount || numAmount > maxAmount) {
        return res.status(400).json({
          success: false,
          error: `Amount must be between ${minAmount} and ${maxAmount} ${currency} for this product`,
        });
      }
    }

    // Sanitize memo
    const sanitizedMemo = memo ? sanitizeString(memo) : null;

    // Generate order ID and payment expiration from settings
    const orderId = uuidv4();
    const paymentTimeoutMinutes = paymentSettings.paymentTimeoutMinutes;
    const paymentExpiresAt = new Date(Date.now() + paymentTimeoutMinutes * 60 * 1000);

    // Generate real TON payment address
    const paymentInfo = await generatePaymentAddress(orderId);
    const paymentAddress = paymentInfo.address;

    // Create order
    const orderResult = await executeQuery(
      `INSERT INTO orders (id, user_id, status, amount, currency, memo, payment_address, payment_expires_at, product_id, quantity)
       VALUES ($1, $2, 'pending', $3, $4, $5, $6, $7, $8, $9)
       RETURNING id, status, amount, currency, memo, payment_address, payment_expires_at, product_id, quantity, created_at`,
      [orderId, userId, numAmount, currency, sanitizedMemo, paymentAddress, paymentExpiresAt, productId, quantity]
    );

    const order = orderResult.rows[0];

    // Get user info for email
    const userResult = await executeQuery(
      'SELECT email, telegram_id FROM users WHERE id = $1',
      [userId]
    );
    const user = userResult.rows[0];

    // Send order confirmation email
    try {
      await sendEmail({
        to: user.email,
        subject: 'Order Confirmation - TON Voucher Platform',
        template: 'order-confirmation',
        data: {
          userName: user.telegram_id,
          orderDetails: {
            orderId: order.id,
            amount: order.amount,
            currency: order.currency,
            status: order.status,
            createdAt: order.created_at,
          },
          paymentAddress: order.payment_address,
          expiresIn: '30 minutes',
        },
      });
    } catch (emailError) {
      logger.error('Failed to send order confirmation email:', emailError);
    }

    logPaymentEvent('ORDER_CREATED', order.id, order.amount, {
      userId,
      currency: order.currency,
      paymentAddress: order.payment_address,
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order: {
          id: order.id,
          status: order.status,
          amount: order.amount,
          currency: order.currency,
          memo: order.memo,
          paymentAddress: order.payment_address,
          paymentExpiresAt: order.payment_expires_at,
          createdAt: order.created_at,
        },
      },
    });
  } catch (error) {
    logger.error('Create order error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create order',
    });
  }
};

// Get order by ID
export const getOrderById = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { id } = req.params;

    const orderResult = await executeQuery(
      `SELECT o.id, o.status, o.amount, o.currency, o.memo, o.payment_address, 
              o.payment_expires_at, o.transaction_hash, o.created_at, o.updated_at,
              v.id as voucher_id, v.code as voucher_code, v.status as voucher_status
       FROM orders o
       LEFT JOIN vouchers v ON o.id = v.order_id
       WHERE o.id = $1 AND o.user_id = $2`,
      [id, userId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    res.json({
      success: true,
      data: {
        order: {
          id: order.id,
          status: order.status,
          amount: order.amount,
          currency: order.currency,
          memo: order.memo,
          paymentAddress: order.payment_address,
          paymentExpiresAt: order.payment_expires_at,
          transactionHash: order.transaction_hash,
          createdAt: order.created_at,
          updatedAt: order.updated_at,
          voucher: order.voucher_id ? {
            id: order.voucher_id,
            code: order.voucher_code,
            status: order.voucher_status,
          } : null,
        },
      },
    });
  } catch (error) {
    logger.error('Get order error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get order',
    });
  }
};

// Get all orders for user
export const getUserOrders = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;

    // Build query conditions
    let whereClause = 'WHERE user_id = $1';
    const queryParams: any[] = [userId];

    if (status) {
      whereClause += ' AND status = $2';
      queryParams.push(status);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM orders ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get orders
    const ordersQuery = `
      SELECT id, status, amount, currency, memo, payment_address, 
             payment_expires_at, transaction_hash, created_at, updated_at
      FROM orders 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const ordersResult = await executeQuery(ordersQuery, queryParams);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        orders: ordersResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get user orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get orders',
    });
  }
};

// Cancel order
export const cancelOrder = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { id } = req.params;

    // Get order
    const orderResult = await executeQuery(
      'SELECT id, status, amount FROM orders WHERE id = $1 AND user_id = $2',
      [id, userId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Check if order can be cancelled
    if (!['pending', 'payment_pending'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        error: 'Order cannot be cancelled',
      });
    }

    // Update order status
    await executeQuery(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      ['cancelled', id]
    );

    logPaymentEvent('ORDER_CANCELLED', order.id, order.amount, { userId });

    res.json({
      success: true,
      message: 'Order cancelled successfully',
    });
  } catch (error) {
    logger.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel order',
    });
  }
};

// Update order status (internal use)
export const updateOrderStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status, transactionHash } = req.body;

    // Validate status
    const validStatuses = ['pending', 'payment_pending', 'paid', 'completed', 'cancelled', 'failed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status',
      });
    }

    // Get current order
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount FROM orders WHERE id = $1',
      [id]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Update order
    const updateQuery = transactionHash
      ? 'UPDATE orders SET status = $1, transaction_hash = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3'
      : 'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2';
    
    const updateParams = transactionHash ? [status, transactionHash, id] : [status, id];
    
    await executeQuery(updateQuery, updateParams);

    logPaymentEvent('ORDER_STATUS_UPDATED', order.id, order.amount, {
      userId: order.user_id,
      oldStatus: order.status,
      newStatus: status,
      transactionHash,
    });

    res.json({
      success: true,
      message: 'Order status updated successfully',
    });
  } catch (error) {
    logger.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order status',
    });
  }
};

// Get order statistics
export const getOrderStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const statsResult = await executeQuery(
      `SELECT 
         COUNT(*) as total_orders,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
         COUNT(CASE WHEN status = 'pending' OR status = 'payment_pending' THEN 1 END) as pending_orders,
         COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_spent
       FROM orders 
       WHERE user_id = $1`,
      [userId]
    );

    const stats = statsResult.rows[0];

    res.json({
      success: true,
      data: {
        stats: {
          totalOrders: parseInt(stats.total_orders),
          completedOrders: parseInt(stats.completed_orders),
          pendingOrders: parseInt(stats.pending_orders),
          totalSpent: parseFloat(stats.total_spent),
        },
      },
    });
  } catch (error) {
    logger.error('Get order stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get order statistics',
    });
  }
};
