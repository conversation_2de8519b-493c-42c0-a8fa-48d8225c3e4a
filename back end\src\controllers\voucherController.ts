import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logVoucherEvent } from '../config/logger';
import { generateVoucherCode, sanitizeString } from '../utils/helpers';
import { sendEmail } from '../services/emailService';
import { settingsService } from '../services/settingsService';

// Generate voucher for completed order
export const generateVoucher = async (orderId: string, userId: string) => {
  try {
    // Check if voucher already exists for this order
    const existingVoucher = await executeQuery(
      'SELECT id FROM vouchers WHERE order_id = $1',
      [orderId]
    );

    if (existingVoucher.rows.length > 0) {
      logger.warn('Voucher already exists for order', { orderId });
      return existingVoucher.rows[0];
    }

    // Get business rule settings for voucher code generation
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();

    // Generate unique voucher code
    let voucherCode: string;
    let isUnique = false;
    let attempts = 0;

    do {
      voucherCode = generateVoucherCode(businessRuleSettings.voucherCodeLength);
      const codeCheck = await executeQuery(
        'SELECT id FROM vouchers WHERE code = $1',
        [voucherCode]
      );
      isUnique = codeCheck.rows.length === 0;
      attempts++;
    } while (!isUnique && attempts < 10);

    if (!isUnique) {
      throw new Error('Failed to generate unique voucher code');
    }

    // Set expiry date using database settings
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + businessRuleSettings.voucherExpiryDays);

    // Create voucher
    const voucherResult = await executeQuery(
      `INSERT INTO vouchers (id, order_id, code, expires_at)
       VALUES ($1, $2, $3, $4)
       RETURNING id, code, status, expires_at, created_at`,
      [uuidv4(), orderId, voucherCode, expiresAt]
    );

    const voucher = voucherResult.rows[0];

    // Get order and user details for email
    const orderUserResult = await executeQuery(
      `SELECT o.id, o.amount, o.currency, o.created_at, u.email, u.telegram_id
       FROM orders o
       JOIN users u ON o.user_id = u.id
       WHERE o.id = $1`,
      [orderId]
    );

    if (orderUserResult.rows.length > 0) {
      const orderUser = orderUserResult.rows[0];

      // Send voucher delivery email
      try {
        await sendEmail({
          to: orderUser.email,
          subject: 'Your Voucher Code is Ready! - TON Voucher Platform',
          template: 'voucher-delivery',
          data: {
            userName: orderUser.telegram_id,
            voucherCode: voucher.code,
            orderDetails: {
              orderId: orderUser.id,
              amount: orderUser.amount,
              currency: orderUser.currency,
              createdAt: orderUser.created_at,
            },
          },
        });
      } catch (emailError) {
        logger.error('Failed to send voucher delivery email:', emailError);
      }
    }

    logVoucherEvent('VOUCHER_GENERATED', voucher.code, userId, {
      orderId,
      expiresAt: voucher.expires_at,
    });

    return voucher;
  } catch (error) {
    logger.error('Generate voucher error:', error);
    throw error;
  }
};

// Get voucher by code
export const getVoucherByCode = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    const userId = req.user!.id;

    const voucherResult = await executeQuery(
      `SELECT v.id, v.code, v.status, v.redeemed_at, v.redeemed_by_user_id, 
              v.expires_at, v.created_at, o.id as order_id, o.amount, o.currency
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       WHERE v.code = $1 AND o.user_id = $2`,
      [code, userId]
    );

    if (voucherResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found',
      });
    }

    const voucher = voucherResult.rows[0];

    res.json({
      success: true,
      data: {
        voucher: {
          id: voucher.id,
          code: voucher.code,
          status: voucher.status,
          redeemedAt: voucher.redeemed_at,
          redeemedByUserId: voucher.redeemed_by_user_id,
          expiresAt: voucher.expires_at,
          createdAt: voucher.created_at,
          order: {
            id: voucher.order_id,
            amount: voucher.amount,
            currency: voucher.currency,
          },
        },
      },
    });
  } catch (error) {
    logger.error('Get voucher by code error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voucher',
    });
  }
};

// Get all user vouchers
export const getUserVouchers = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;
    const search = req.query.search as string;

    // Build query conditions
    let whereClause = 'WHERE o.user_id = $1';
    const queryParams: any[] = [userId];

    if (status && status !== 'all') {
      whereClause += ` AND v.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    if (search) {
      whereClause += ` AND (v.code ILIKE $${queryParams.length + 1} OR o.memo ILIKE $${queryParams.length + 1})`;
      queryParams.push(`%${search}%`);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*)
      FROM vouchers v
      JOIN orders o ON v.order_id = o.id
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get vouchers with enhanced data for frontend
    const vouchersQuery = `
      SELECT v.id, v.code, v.status, v.redeemed_at, v.expires_at, v.created_at,
             o.id as order_id, o.amount, o.currency, o.memo,
             CASE
               WHEN v.status = 'active' AND v.expires_at > CURRENT_TIMESTAMP THEN 'active'
               WHEN v.status = 'active' AND v.expires_at <= CURRENT_TIMESTAMP THEN 'expired'
               ELSE v.status
             END as computed_status
      FROM vouchers v
      JOIN orders o ON v.order_id = o.id
      ${whereClause}
      ORDER BY v.created_at DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const vouchersResult = await executeQuery(vouchersQuery, queryParams);

    // Transform data to match frontend expectations
    const transformedVouchers = vouchersResult.rows.map((row: any) => ({
      id: row.id,
      code: row.code,
      type: 'Digital Voucher', // Default type for now
      amount: row.amount,
      currency: row.currency,
      status: row.computed_status,
      createdAt: row.created_at,
      expiresAt: row.expires_at,
      redeemedAt: row.redeemed_at,
      orderId: row.order_id,
      instructions: row.memo || 'Present this voucher code to redeem your purchase.',
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        vouchers: transformedVouchers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get user vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vouchers',
    });
  }
};

// Redeem voucher (mark as used)
export const redeemVoucher = async (req: Request, res: Response) => {
  try {
    const { code } = req.body;
    const userId = req.user!.id;

    // Get voucher details
    const voucherResult = await executeQuery(
      `SELECT v.id, v.code, v.status, v.expires_at, o.user_id as order_user_id
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       WHERE v.code = $1`,
      [code]
    );

    if (voucherResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found',
      });
    }

    const voucher = voucherResult.rows[0];

    // Check if user owns this voucher
    if (voucher.order_user_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'You do not own this voucher',
      });
    }

    // Check voucher status
    if (voucher.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: `Voucher is ${voucher.status} and cannot be redeemed`,
      });
    }

    // Check if voucher is expired
    if (new Date() > new Date(voucher.expires_at)) {
      // Update voucher status to expired
      await executeQuery(
        'UPDATE vouchers SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['expired', voucher.id]
      );

      return res.status(400).json({
        success: false,
        error: 'Voucher has expired',
      });
    }

    // Mark voucher as redeemed
    await executeQuery(
      `UPDATE vouchers 
       SET status = $1, redeemed_at = CURRENT_TIMESTAMP, redeemed_by_user_id = $2
       WHERE id = $3`,
      ['redeemed', userId, voucher.id]
    );

    logVoucherEvent('VOUCHER_REDEEMED', voucher.code, userId, {
      voucherId: voucher.id,
    });

    res.json({
      success: true,
      message: 'Voucher redeemed successfully',
      data: {
        code: voucher.code,
        redeemedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Redeem voucher error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to redeem voucher',
    });
  }
};

// Check voucher status (public endpoint for verification)
export const checkVoucherStatus = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;

    const voucherResult = await executeQuery(
      'SELECT code, status, expires_at, redeemed_at FROM vouchers WHERE code = $1',
      [code]
    );

    if (voucherResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found',
      });
    }

    const voucher = voucherResult.rows[0];

    // Check if expired but not marked as such
    if (voucher.status === 'active' && new Date() > new Date(voucher.expires_at)) {
      await executeQuery(
        'UPDATE vouchers SET status = $1 WHERE code = $2',
        ['expired', code]
      );
      voucher.status = 'expired';
    }

    res.json({
      success: true,
      data: {
        code: voucher.code,
        status: voucher.status,
        expiresAt: voucher.expires_at,
        redeemedAt: voucher.redeemed_at,
        isValid: voucher.status === 'active' && new Date() <= new Date(voucher.expires_at),
      },
    });
  } catch (error) {
    logger.error('Check voucher status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check voucher status',
    });
  }
};

// Get voucher statistics
export const getVoucherStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const statsResult = await executeQuery(
      `SELECT
         COUNT(*) as total_vouchers,
         COUNT(CASE WHEN v.status = 'active' THEN 1 END) as active_vouchers,
         COUNT(CASE WHEN v.status = 'redeemed' THEN 1 END) as redeemed_vouchers,
         COUNT(CASE WHEN v.status = 'expired' THEN 1 END) as expired_vouchers
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       WHERE o.user_id = $1`,
      [userId]
    );

    const stats = statsResult.rows[0];

    res.json({
      success: true,
      data: {
        stats: {
          totalVouchers: parseInt(stats.total_vouchers),
          activeVouchers: parseInt(stats.active_vouchers),
          redeemedVouchers: parseInt(stats.redeemed_vouchers),
          expiredVouchers: parseInt(stats.expired_vouchers),
        },
      },
    });
  } catch (error) {
    logger.error('Get voucher stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voucher statistics',
    });
  }
};

// Download voucher details as text file
export const downloadVoucherDetails = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    const userId = req.user!.id;

    const voucherResult = await executeQuery(
      `SELECT v.id, v.code, v.status, v.redeemed_at, v.expires_at, v.created_at,
              o.id as order_id, o.amount, o.currency, o.memo, u.telegram_id
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       JOIN users u ON o.user_id = u.id
       WHERE v.code = $1 AND o.user_id = $2`,
      [code, userId]
    );

    if (voucherResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found',
      });
    }

    const voucher = voucherResult.rows[0];

    // Generate voucher details text content
    const content = `TON VOUCHER PLATFORM - VOUCHER DETAILS
=====================================

Voucher Code: ${voucher.code}
Status: ${voucher.status.toUpperCase()}
Amount: ${voucher.amount} ${voucher.currency}
Order ID: ${voucher.order_id}

Created: ${new Date(voucher.created_at).toLocaleString()}
Expires: ${new Date(voucher.expires_at).toLocaleString()}
${voucher.redeemed_at ? `Redeemed: ${new Date(voucher.redeemed_at).toLocaleString()}` : ''}

Owner: ${voucher.telegram_id}
${voucher.memo ? `Instructions: ${voucher.memo}` : 'Instructions: Present this voucher code to redeem your purchase.'}

=====================================
Generated on: ${new Date().toLocaleString()}
Platform: TON Voucher Platform
`;

    // Set headers for file download
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="voucher-${voucher.code}.txt"`);
    res.setHeader('Content-Length', Buffer.byteLength(content, 'utf8'));

    logVoucherEvent('VOUCHER_DOWNLOADED', voucher.code, userId, {
      voucherId: voucher.id,
    });

    res.send(content);
  } catch (error) {
    logger.error('Download voucher details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download voucher details',
    });
  }
};
