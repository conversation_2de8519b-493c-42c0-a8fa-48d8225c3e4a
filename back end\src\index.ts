import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Validate environment configuration early
import { env, checkProductionReadiness } from './config/env-validation';

import app, { initializeDynamicMiddleware } from './app';
import { settingsInitializationService } from './services/settingsInitializationService';
import { logger } from './config/logger';
import { connectDatabases, checkDatabaseHealth } from './config/database';
import { initializeTonClient, checkTonServiceHealth } from './services/tonService';
import { setupAdmin } from './scripts/setup-admin';
import { initializeWebSocketServer } from './controllers/realtimeSettingsController';

const PORT = env.PORT;

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Check production readiness if in production
if (env.NODE_ENV === 'production') {
  const { ready, issues } = checkProductionReadiness();
  if (!ready) {
    logger.error('Production readiness check failed:', issues);
    process.exit(1);
  }
  logger.info('Production readiness check passed');
}

// Start server
const startServer = async () => {
  try {
    // Connect to databases
    logger.info('Connecting to databases...');
    await connectDatabases();

    // Check database health
    const health = await checkDatabaseHealth();
    if (!health.postgres || !health.redis) {
      throw new Error('Database health check failed');
    }

    logger.info('Database connections established successfully');

    // Initialize all dynamic settings
    logger.info('Initializing all dynamic settings...');
    await settingsInitializationService.initializeAllSettings();

    // Initialize dynamic middleware with database settings
    logger.info('Initializing dynamic middleware...');
    await initializeDynamicMiddleware();

    // Setup admin user if required
    if (env.ADMIN_SETUP_REQUIRED) {
      logger.info('Setting up admin user...');
      await setupAdmin();
    }

    // Initialize TON client
    logger.info('Initializing TON client...');
    await initializeTonClient();

    // Check TON service health
    const tonHealth = await checkTonServiceHealth();
    if (!tonHealth.healthy) {
      logger.warn('TON service health check failed:', tonHealth.error);
      // Don't fail startup, but log the warning
    } else {
      logger.info('TON service initialized successfully');
    }
    
    // Start HTTP server
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 API URL: http://localhost:${PORT}/api/v1`);
      logger.info(`💚 Health check: http://localhost:${PORT}/health`);
    });

    // Initialize WebSocket server for real-time settings updates
    logger.info('Initializing WebSocket server for real-time settings...');
    initializeWebSocketServer(server);
    logger.info(`🔄 WebSocket server initialized at ws://localhost:${PORT}/ws/admin/settings`);

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      
      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });
      
      // Force close after 10 seconds
      setTimeout(() => {
        logger.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();
