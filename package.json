{"name": "tonsite-voucher-platform", "version": "1.0.0", "description": "Secure e-commerce platform for selling premium game voucher codes with TON cryptocurrency payments", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm run dev", "backend:build": "cd backend && npm run build", "frontend:build": "cd frontend && npm run build", "backend:start": "cd backend && npm start", "frontend:start": "cd frontend && npm start", "test": "npm run backend:test && npm run frontend:test", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test", "lint": "npm run backend:lint && npm run frontend:lint", "backend:lint": "cd backend && npm run lint", "frontend:lint": "cd frontend && npm run lint", "install:all": "npm install && npm run backend:install && npm run frontend:install && npm run shared:install", "backend:install": "cd backend && npm install", "frontend:install": "cd frontend && npm install", "shared:install": "cd shared && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules shared/node_modules", "security:audit": "npm audit && npm run backend:audit && npm run frontend:audit", "backend:audit": "cd backend && npm audit", "frontend:audit": "cd frontend && npm audit"}, "devDependencies": {"concurrently": "^8.2.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tonsite-voucher-platform.git"}, "keywords": ["ecommerce", "voucher", "ton", "cryptocurrency", "security", "blockchain"], "author": "Your Name", "license": "MIT"}