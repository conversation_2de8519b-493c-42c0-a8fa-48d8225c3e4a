/**
 * Dynamic Validation Middleware
 * 
 * This middleware provides validation functions that use database settings
 * instead of hardcoded values for production readiness.
 */

import { Request, Response, NextFunction } from 'express';
import { body, query, ValidationChain } from 'express-validator';
import { getPaymentSettings, getVoucherSettings, settingsService } from '../services/settingsService';
import { logger } from '../config/logger';

/**
 * Create dynamic currency validation based on payment settings
 */
export const createCurrencyValidation = async (): Promise<ValidationChain> => {
  try {
    const paymentSettings = await getPaymentSettings();
    const supportedCurrencies = paymentSettings.supportedCurrencies || ['TON', 'USD', 'EUR'];
    
    logger.debug('Dynamic currency validation created', { supportedCurrencies });
    
    return body('currency')
      .optional()
      .isIn(supportedCurrencies)
      .withMessage(`Currency must be one of: ${supportedCurrencies.join(', ')}`);
  } catch (error) {
    logger.error('Failed to create dynamic currency validation, using fallback:', error);
    // Fallback to default currencies if settings fail
    return body('currency')
      .optional()
      .isIn(['TON', 'USD', 'EUR'])
      .withMessage('Currency must be TON, USD, or EUR');
  }
};

/**
 * Create dynamic amount validation based on business rule settings
 */
export const createAmountValidation = async (field: string = 'amount'): Promise<ValidationChain> => {
  try {
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();
    const minAmount = parseFloat(businessRuleSettings.minOrderAmount);
    const maxAmount = parseFloat(businessRuleSettings.maxOrderAmount);

    logger.debug('Dynamic amount validation created', { field, minAmount, maxAmount });

    return body(field)
      .isFloat({ min: minAmount, max: maxAmount })
      .withMessage(`Amount must be between ${minAmount} and ${maxAmount}`);
  } catch (error) {
    logger.error('Failed to create dynamic amount validation, using fallback:', error);
    // Fallback to default amounts if settings fail
    return body(field)
      .isFloat({ min: 0.01, max: 1000000 })
      .withMessage('Amount must be between 0.01 and 1000000 (fallback validation)');
  }
};

/**
 * Create dynamic voucher quantity validation based on voucher settings
 */
export const createVoucherQuantityValidation = async (): Promise<ValidationChain> => {
  try {
    const voucherSettings = await getVoucherSettings();
    const maxVouchersPerOrder = voucherSettings.maxVouchersPerOrder || 10;
    
    logger.debug('Dynamic voucher quantity validation created', { maxVouchersPerOrder });
    
    return body('quantity')
      .optional()
      .isInt({ min: 1, max: maxVouchersPerOrder })
      .withMessage(`Quantity must be between 1 and ${maxVouchersPerOrder}`);
  } catch (error) {
    logger.error('Failed to create dynamic voucher quantity validation, using fallback:', error);
    // Fallback to default quantity if settings fail
    return body('quantity')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Quantity must be between 1 and 10 (fallback validation)');
  }
};

/**
 * Create dynamic expiry days validation based on voucher settings
 */
export const createExpiryDaysValidation = async (): Promise<ValidationChain> => {
  try {
    const voucherSettings = await getVoucherSettings();
    const defaultExpiryDays = voucherSettings.defaultExpiryDays || 365;
    const maxExpiryDays = Math.min(defaultExpiryDays * 10, 3650); // Max 10x default or 10 years
    
    logger.debug('Dynamic expiry days validation created', { defaultExpiryDays, maxExpiryDays });
    
    return body('expiryDays')
      .optional()
      .isInt({ min: 1, max: maxExpiryDays })
      .withMessage(`Expiry days must be between 1 and ${maxExpiryDays}`);
  } catch (error) {
    logger.error('Failed to create dynamic expiry days validation, using fallback:', error);
    // Fallback to default expiry if settings fail
    return body('expiryDays')
      .optional()
      .isInt({ min: 1, max: 3650 })
      .withMessage('Expiry days must be between 1 and 3650 (fallback validation)');
  }
};

/**
 * Create dynamic pagination validation based on pagination settings
 */
export const createPaginationValidation = async (): Promise<ValidationChain[]> => {
  try {
    const paginationSettings = await settingsService.getPaginationSettings();
    const maxLimit = paginationSettings.maxLimit;

    logger.debug('Dynamic pagination validation created', { maxLimit });

    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: maxLimit })
        .withMessage(`Limit must be between 1 and ${maxLimit}`),
    ];
  } catch (error) {
    logger.error('Failed to create dynamic pagination validation, using fallback:', error);
    // Fallback to default limit if settings fail
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100 (fallback validation)'),
    ];
  }
};

/**
 * Create dynamic pagination limit validation based on pagination settings
 */
export const createPaginationLimitValidation = async (): Promise<ValidationChain> => {
  try {
    const paginationSettings = await settingsService.getPaginationSettings();
    const maxLimit = paginationSettings.maxLimit;

    logger.debug('Dynamic pagination limit validation created', { maxLimit });

    return body('limit')
      .optional()
      .isInt({ min: 1, max: maxLimit })
      .withMessage(`Limit must be between 1 and ${maxLimit}`);
  } catch (error) {
    logger.error('Failed to create dynamic pagination limit validation, using fallback:', error);
    // Fallback to default limit if settings fail
    return body('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100 (fallback validation)');
  }
};

/**
 * Create dynamic file upload validation based on file upload settings
 */
export const createFileUploadValidation = async () => {
  try {
    const fileUploadSettings = await settingsService.getFileUploadSettings();
    const maxFileSize = fileUploadSettings.maxFileSize;
    const allowedMimeTypes = fileUploadSettings.allowedMimeTypes;

    logger.debug('Dynamic file upload validation created', { maxFileSize, allowedMimeTypes });

    return {
      maxFileSize,
      allowedMimeTypes,
      fileFilter: (_req: any, file: any, cb: any) => {
        if (allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error(`File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`), false);
        }
      },
    };
  } catch (error) {
    logger.error('Failed to create dynamic file upload validation, using fallback:', error);
    // Fallback to default settings if database fails
    return {
      maxFileSize: 5 * 1024 * 1024, // 5MB (fallback value)
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      fileFilter: (_req: any, file: any, cb: any) => {
        const defaultTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (defaultTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error(`File type not allowed. Allowed types: ${defaultTypes.join(', ')}`), false);
        }
      },
    };
  }
};



/**
 * Middleware to create and apply dynamic validations
 * This is used when you need to create validations at request time
 */
export const dynamicValidationMiddleware = (
  validationCreators: Array<() => Promise<ValidationChain>>
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    try {
      // Create all dynamic validations
      const validations = await Promise.all(
        validationCreators.map(creator => creator())
      );
      
      // Run all validations
      await Promise.all(validations.map(validation => validation.run(req)));
      
      next();
    } catch (error) {
      logger.error('Dynamic validation middleware error:', error);
      return res.status(500).json({
        success: false,
        error: 'Validation configuration error',
      });
    }
  };
};

/**
 * Cache for dynamic validations to avoid recreating them on every request
 */
class ValidationCache {
  private cache: Map<string, { validation: ValidationChain; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  async get(key: string, creator: () => Promise<ValidationChain>): Promise<ValidationChain> {
    const cached = this.cache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < this.cacheTimeout) {
      return cached.validation;
    }

    // Create new validation and cache it
    const validation = await creator();
    this.cache.set(key, { validation, timestamp: now });
    
    return validation;
  }

  clear(): void {
    this.cache.clear();
  }
}

export const validationCache = new ValidationCache();

/**
 * Cached version of dynamic validation creators
 */
export const getCachedCurrencyValidation = () => 
  validationCache.get('currency', createCurrencyValidation);

export const getCachedAmountValidation = (field: string = 'amount') => 
  validationCache.get(`amount_${field}`, () => createAmountValidation(field));

export const getCachedVoucherQuantityValidation = () => 
  validationCache.get('voucher_quantity', createVoucherQuantityValidation);

export const getCachedExpiryDaysValidation = () => 
  validationCache.get('expiry_days', createExpiryDaysValidation);

export const getCachedPaginationLimitValidation = () => 
  validationCache.get('pagination_limit', createPaginationLimitValidation);
