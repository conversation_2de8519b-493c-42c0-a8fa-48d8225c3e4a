import { Pool, PoolConfig } from 'pg';
import { createClient, RedisClientType } from 'redis';
import { logger, logDatabaseOperation, logDatabaseError } from './logger';
import { env } from './env-validation';

// PostgreSQL Configuration using validated environment config
const dbConfig: PoolConfig = {
  host: env.DATABASE_HOST,
  port: env.DATABASE_PORT,
  database: env.DATABASE_NAME,
  user: env.DATABASE_USER,
  password: env.DATABASE_PASSWORD,
  // Environment-aware SSL configuration
  ssl: env.NODE_ENV === 'production'
    ? { rejectUnauthorized: true }  // Production: Strict SSL
    : env.DATABASE_SSL
      ? { rejectUnauthorized: false }  // Dev: Optional SSL for testing
      : false,  // Local: No SSL overhead
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times
};

// Log database configuration (without sensitive data)
logger.debug('Database configuration:', {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  user: dbConfig.user,
  passwordLength: dbConfig.password?.length,
  ssl: dbConfig.ssl
});

// Create PostgreSQL connection pool
export const db = new Pool(dbConfig);

// PostgreSQL connection event handlers
db.on('connect', () => {
  logger.info('Connected to PostgreSQL database');
});

db.on('error', (err) => {
  logger.error('PostgreSQL connection error:', err);
});

// Redis Configuration using validated environment config
const redisConfig: any = {
  socket: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
  },
  database: env.REDIS_DB,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
};

// Add password only if it exists and is not empty
if (env.REDIS_PASSWORD && env.REDIS_PASSWORD.length > 0) {
  redisConfig.password = env.REDIS_PASSWORD;
}

// Create Redis client
export const redis: RedisClientType = createClient(redisConfig);

// Redis connection event handlers
redis.on('connect', () => {
  logger.info('Connected to Redis server');
});

redis.on('error', (err) => {
  logger.error('Redis connection error:', err);
});

redis.on('ready', () => {
  logger.info('Redis client ready');
});

redis.on('reconnecting', () => {
  logger.warn('Redis client reconnecting...');
});

// Database connection functions
export const connectDatabases = async (): Promise<void> => {
  try {
    // Test PostgreSQL connection
    const client = await db.connect();
    await client.query('SELECT NOW()');
    client.release();
    logger.info('PostgreSQL connection test successful');

    // Connect to Redis
    await redis.connect();
    logger.info('Redis connection successful');
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

export const disconnectDatabases = async (): Promise<void> => {
  try {
    await db.end();
    await redis.quit();
    logger.info('Database connections closed');
  } catch (error) {
    logger.error('Error closing database connections:', error);
    throw error;
  }
};

// Database health check
export const checkDatabaseHealth = async (): Promise<{ postgres: boolean; redis: boolean }> => {
  const health = { postgres: false, redis: false };

  try {
    // Check PostgreSQL
    const client = await db.connect();
    await client.query('SELECT 1');
    client.release();
    health.postgres = true;
  } catch (error) {
    logger.error('PostgreSQL health check failed:', error);
  }

  try {
    // Check Redis
    await redis.ping();
    health.redis = true;
  } catch (error) {
    logger.error('Redis health check failed:', error);
  }

  return health;
};

// Enhanced utility functions for database operations with comprehensive logging
export const executeQuery = async (text: string, params?: any[], context?: any): Promise<any> => {
  const client = await db.connect();
  const startTime = Date.now();

  try {
    const result = await client.query(text, params);
    const duration = Date.now() - startTime;

    // Log successful database operations
    logDatabaseOperation(
      'SELECT/INSERT/UPDATE/DELETE',
      extractTableName(text),
      duration,
      true,
      { rowCount: result.rowCount, ...context }
    );

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Log database errors with comprehensive context
    const errorId = logDatabaseError(
      error as Error,
      text,
      params,
      { duration, ...context }
    );

    // Log failed database operation
    logDatabaseOperation(
      'FAILED_QUERY',
      extractTableName(text),
      duration,
      false,
      { errorId, ...context }
    );

    throw error;
  } finally {
    client.release();
  }
};

export const executeTransaction = async (queries: Array<{ text: string; params?: any[] }>, context?: any): Promise<any[]> => {
  const client = await db.connect();
  const startTime = Date.now();
  const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    await client.query('BEGIN');
    const results = [];

    logger.debug('Transaction Started', {
      transactionId,
      queryCount: queries.length,
      context,
      timestamp: new Date().toISOString(),
    });

    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];
      const queryStartTime = Date.now();

      try {
        const result = await client.query(query.text, query.params);
        const queryDuration = Date.now() - queryStartTime;

        logDatabaseOperation(
          `TRANSACTION_QUERY_${i + 1}`,
          extractTableName(query.text),
          queryDuration,
          true,
          { transactionId, rowCount: result.rowCount, ...context }
        );

        results.push(result);
      } catch (queryError) {
        const queryDuration = Date.now() - queryStartTime;

        logDatabaseError(
          queryError as Error,
          query.text,
          query.params,
          { transactionId, queryIndex: i, queryDuration, ...context }
        );

        throw queryError;
      }
    }

    await client.query('COMMIT');
    const totalDuration = Date.now() - startTime;

    logger.info('Transaction Committed', {
      transactionId,
      queryCount: queries.length,
      totalDuration,
      context,
      timestamp: new Date().toISOString(),
    });

    return results;
  } catch (error) {
    await client.query('ROLLBACK');
    const totalDuration = Date.now() - startTime;

    logger.error('Transaction Rolled Back', {
      transactionId,
      queryCount: queries.length,
      totalDuration,
      error: (error as Error).message,
      context,
      timestamp: new Date().toISOString(),
    });

    throw error;
  } finally {
    client.release();
  }
};

// Helper function to extract table name from SQL query
const extractTableName = (query: string): string => {
  const normalizedQuery = query.trim().toLowerCase();

  // Match common SQL patterns
  const patterns = [
    /(?:from|into|update|delete\s+from)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i,
    /(?:insert\s+into)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i,
    /(?:create\s+table)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i,
    /(?:alter\s+table)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i,
    /(?:drop\s+table)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i,
  ];

  for (const pattern of patterns) {
    const match = normalizedQuery.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return 'unknown_table';
};

// Redis utility functions
export const setCache = async (key: string, value: any, ttl?: number): Promise<void> => {
  try {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await redis.setEx(key, ttl, serializedValue);
    } else {
      await redis.set(key, serializedValue);
    }
  } catch (error) {
    logger.error('Redis set error:', { key, error });
    throw error;
  }
};

export const getCache = async (key: string): Promise<any> => {
  try {
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error('Redis get error:', { key, error });
    return null;
  }
};

export const deleteCache = async (key: string): Promise<void> => {
  try {
    await redis.del(key);
  } catch (error) {
    logger.error('Redis delete error:', { key, error });
    throw error;
  }
};

export const setCacheWithPattern = async (pattern: string, value: any, ttl?: number): Promise<void> => {
  try {
    const keys = await redis.keys(pattern);
    const pipeline = redis.multi();
    
    keys.forEach(key => {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        pipeline.setEx(key, ttl, serializedValue);
      } else {
        pipeline.set(key, serializedValue);
      }
    });
    
    await pipeline.exec();
  } catch (error) {
    logger.error('Redis pattern set error:', { pattern, error });
    throw error;
  }
};

export const deleteCachePattern = async (pattern: string): Promise<void> => {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(keys);
    }
  } catch (error) {
    logger.error('Redis pattern delete error:', { pattern, error });
    throw error;
  }
};
