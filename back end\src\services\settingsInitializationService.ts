import { logger } from '../config/logger';
import { settingsService } from './settingsService';
import { updateRateLimiters } from '../middleware/security';

/**
 * Settings Initialization Service
 * 
 * This service manages the initialization and refresh of all dynamic settings
 * throughout the application. It ensures that all middleware, validation rules,
 * and other components that depend on database settings are properly initialized
 * and can be refreshed when settings change.
 */
class SettingsInitializationService {
  private initialized = false;
  private refreshCallbacks: Array<() => Promise<void>> = [];

  /**
   * Initialize all dynamic settings-dependent components
   */
  async initializeAllSettings(): Promise<void> {
    if (this.initialized) {
      logger.debug('Settings already initialized, skipping...');
      return;
    }

    try {
      logger.info('Initializing all dynamic settings...');

      // Initialize rate limiters with database settings
      await this.initializeRateLimiters();

      // Initialize validation rules cache
      await this.initializeValidationRules();

      // Initialize middleware settings
      await this.initializeMiddlewareSettings();

      // Initialize business rule settings
      await this.initializeBusinessRuleSettings();

      // Initialize security settings
      await this.initializeSecuritySettings();

      // Initialize file upload settings
      await this.initializeFileUploadSettings();

      // Initialize pagination settings
      await this.initializePaginationSettings();

      // Initialize cache settings
      await this.initializeCacheSettings();

      this.initialized = true;
      logger.info('All dynamic settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize dynamic settings:', error);
      throw error;
    }
  }

  /**
   * Refresh all dynamic settings
   */
  async refreshAllSettings(): Promise<void> {
    try {
      logger.info('Refreshing all dynamic settings...');

      // Clear settings cache first
      await settingsService.clearCache();

      // Refresh rate limiters
      await this.initializeRateLimiters();

      // Execute all registered refresh callbacks
      for (const callback of this.refreshCallbacks) {
        try {
          await callback();
        } catch (error) {
          logger.error('Error executing refresh callback:', error);
        }
      }

      logger.info('All dynamic settings refreshed successfully');
    } catch (error) {
      logger.error('Failed to refresh dynamic settings:', error);
      throw error;
    }
  }

  /**
   * Register a callback to be executed when settings are refreshed
   */
  registerRefreshCallback(callback: () => Promise<void>): void {
    this.refreshCallbacks.push(callback);
  }

  /**
   * Initialize rate limiters with database settings
   */
  private async initializeRateLimiters(): Promise<void> {
    try {
      logger.debug('Initializing rate limiters...');
      await updateRateLimiters();
      logger.debug('Rate limiters initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize rate limiters:', error);
      throw error;
    }
  }

  /**
   * Initialize validation rules cache
   */
  private async initializeValidationRules(): Promise<void> {
    try {
      logger.debug('Initializing validation rules...');
      
      // Pre-warm validation rule cache by calling each validation function
      const { createAmountValidation, createPaginationValidation } = await import('../middleware/dynamicValidation');
      
      // Create validation rules to warm up cache
      await createAmountValidation();
      await createPaginationValidation();
      
      logger.debug('Validation rules initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize validation rules:', error);
      // Don't throw here as validation rules have fallbacks
    }
  }

  /**
   * Initialize middleware settings
   */
  private async initializeMiddlewareSettings(): Promise<void> {
    try {
      logger.debug('Initializing middleware settings...');
      await settingsService.getMiddlewareSettings();
      logger.debug('Middleware settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize middleware settings:', error);
      // Don't throw here as middleware has fallbacks
    }
  }

  /**
   * Initialize business rule settings
   */
  private async initializeBusinessRuleSettings(): Promise<void> {
    try {
      logger.debug('Initializing business rule settings...');
      await settingsService.getBusinessRuleSettings();
      logger.debug('Business rule settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize business rule settings:', error);
      // Don't throw here as business rules have fallbacks
    }
  }

  /**
   * Initialize security settings
   */
  private async initializeSecuritySettings(): Promise<void> {
    try {
      logger.debug('Initializing security settings...');
      await settingsService.getSecuritySettings();
      logger.debug('Security settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize security settings:', error);
      // Don't throw here as security settings have fallbacks
    }
  }

  /**
   * Initialize file upload settings
   */
  private async initializeFileUploadSettings(): Promise<void> {
    try {
      logger.debug('Initializing file upload settings...');
      await settingsService.getFileUploadSettings();
      logger.debug('File upload settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize file upload settings:', error);
      // Don't throw here as file upload settings have fallbacks
    }
  }

  /**
   * Initialize pagination settings
   */
  private async initializePaginationSettings(): Promise<void> {
    try {
      logger.debug('Initializing pagination settings...');
      await settingsService.getPaginationSettings();
      logger.debug('Pagination settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize pagination settings:', error);
      // Don't throw here as pagination settings have fallbacks
    }
  }

  /**
   * Initialize cache settings
   */
  private async initializeCacheSettings(): Promise<void> {
    try {
      logger.debug('Initializing cache settings...');
      await settingsService.getCacheSettings();
      logger.debug('Cache settings initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize cache settings:', error);
      // Don't throw here as cache settings have fallbacks
    }
  }

  /**
   * Check if settings are initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Reset initialization state (for testing)
   */
  reset(): void {
    this.initialized = false;
    this.refreshCallbacks = [];
  }
}

// Export singleton instance
export const settingsInitializationService = new SettingsInitializationService();
