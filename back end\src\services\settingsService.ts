import { executeQuery } from '../config/database';
import { logger } from '../config/logger';

interface SystemSetting {
  category: string;
  setting_key: string;
  setting_value: string;
  data_type: 'string' | 'number' | 'boolean' | 'json';
}

class SettingsService {
  private cache: Map<string, any> = new Map();
  private lastCacheUpdate: number = 0;
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes for non-security settings

  // Security settings are NEVER cached for financial platform security
  private securityCategories = new Set(['security', 'authentication', 'authorization']);
  private criticalCategories = new Set(['payment', 'blockchain', 'security']);

  /**
   * Get setting directly from database (bypasses cache)
   * Used for security-critical settings that must be real-time
   */
  private async getSettingFromDatabase(category: string, key: string, defaultValue?: any): Promise<any> {
    try {
      const result = await executeQuery(
        'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
        [category, key]
      );

      if (result.rows.length === 0) {
        logger.debug(`Setting ${category}.${key} not found in database, using default:`, defaultValue);
        return defaultValue;
      }

      const { setting_value, data_type } = result.rows[0];
      const parsedValue = this.parseSettingValue(setting_value, data_type);

      logger.debug(`Retrieved CRITICAL setting ${category}.${key} from database:`, parsedValue);
      return parsedValue;
    } catch (error) {
      logger.error(`Failed to get setting ${category}.${key} from database:`, error);
      return defaultValue;
    }
  }

  /**
   * Get a setting value by category and key
   * Security settings are NEVER cached for immediate enforcement
   */
  async getSetting(category: string, key: string, defaultValue?: any): Promise<any> {
    const settingKey = `${category}.${key}`;

    // SECURITY: Never cache security-related settings for financial platform
    if (this.securityCategories.has(category) || this.criticalCategories.has(category)) {
      logger.debug(`Getting CRITICAL setting ${settingKey} - bypassing cache for security`);
      return await this.getSettingFromDatabase(category, key, defaultValue);
    }

    // For non-critical settings, use cache
    await this.refreshCacheIfNeeded();
    const cachedValue = this.cache.get(settingKey);

    logger.debug(`Getting setting ${settingKey}, cached: ${cachedValue !== undefined}, cache size: ${this.cache.size}`);

    if (cachedValue !== undefined) {
      logger.debug(`Returning cached value for ${settingKey}:`, cachedValue);
      return cachedValue;
    }

    // If not in cache and no default provided, try to fetch from database
    try {
      logger.debug(`Fetching setting ${settingKey} from database`);
      logger.debug(`Database config check - HOST: ${process.env.DATABASE_HOST}, USER: ${process.env.DATABASE_USER}, PASSWORD type: ${typeof process.env.DATABASE_PASSWORD}, PASSWORD length: ${process.env.DATABASE_PASSWORD?.length}`);

      const result = await executeQuery(
        'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
        [category, key]
      );

      logger.debug(`Database query result for ${settingKey}:`, { rowCount: result.rows.length, rows: result.rows });

      if (result.rows.length > 0) {
        const setting = result.rows[0];
        const value = this.parseSettingValue(setting.setting_value, setting.data_type);
        this.cache.set(settingKey, value);
        logger.debug(`Parsed and cached value for ${settingKey}:`, value);
        return value;
      }
    } catch (error) {
      logger.error(`Failed to fetch setting ${category}.${key}:`, error);
    }

    logger.debug(`Returning default value for ${settingKey}:`, defaultValue);
    return defaultValue;
  }

  /**
   * Get multiple settings by category
   */
  async getSettingsByCategory(category: string): Promise<Record<string, any>> {
    await this.refreshCacheIfNeeded();
    
    const settings: Record<string, any> = {};
    
    try {
      const result = await executeQuery(
        'SELECT setting_key, setting_value, data_type FROM system_settings WHERE category = $1',
        [category]
      );
      
      for (const row of result.rows) {
        const value = this.parseSettingValue(row.setting_value, row.data_type);
        settings[row.setting_key] = value;
        this.cache.set(`${category}.${row.setting_key}`, value);
      }
    } catch (error) {
      logger.error(`Failed to fetch settings for category ${category}:`, error);
    }
    
    return settings;
  }

  /**
   * Get all settings grouped by category
   */
  async getAllSettings(): Promise<Record<string, Record<string, any>>> {
    try {
      const result = await executeQuery(
        'SELECT category, setting_key, setting_value, data_type FROM system_settings ORDER BY category, setting_key'
      );
      
      const allSettings: Record<string, Record<string, any>> = {};
      
      for (const row of result.rows) {
        if (!allSettings[row.category]) {
          allSettings[row.category] = {};
        }
        
        const value = this.parseSettingValue(row.setting_value, row.data_type);
        allSettings[row.category][row.setting_key] = value;
        this.cache.set(`${row.category}.${row.setting_key}`, value);
      }
      
      this.lastCacheUpdate = Date.now();
      return allSettings;
    } catch (error) {
      logger.error('Failed to fetch all settings:', error);
      return {};
    }
  }

  /**
   * Refresh cache if it's stale
   */
  private async refreshCacheIfNeeded(): Promise<void> {
    const now = Date.now();
    if (now - this.lastCacheUpdate > this.cacheTimeout) {
      await this.getAllSettings();
      // Update cache timeout from performance settings after loading
      await this.updateCacheTimeout();
    }
  }

  /**
   * Update cache timeout from performance settings
   */
  private async updateCacheTimeout(): Promise<void> {
    try {
      // Get cache TTL from performance settings (avoid infinite recursion by checking cache first)
      const cacheKey = 'performance.cache_ttl_seconds';
      const cachedValue = this.cache.get(cacheKey);

      if (cachedValue !== undefined) {
        const newTimeout = cachedValue * 1000; // Convert seconds to milliseconds
        if (newTimeout !== this.cacheTimeout) {
          this.cacheTimeout = newTimeout;
          logger.debug(`Updated settings cache timeout to ${newTimeout}ms`);
        }
      }
    } catch (error) {
      logger.error('Failed to update cache timeout from settings:', error);
    }
  }

  /**
   * Parse setting value based on data type
   */
  private parseSettingValue(value: string, dataType: string): any {
    try {
      switch (dataType) {
        case 'boolean':
          return value.toLowerCase() === 'true';
        case 'number':
          return parseFloat(value);
        case 'json':
          return JSON.parse(value);
        case 'string':
        default:
          return value;
      }
    } catch (error) {
      logger.error(`Failed to parse setting value "${value}" as ${dataType}:`, error);
      return value; // Return as string if parsing fails
    }
  }

  /**
   * Clear cache (useful after settings updates)
   */
  clearCache(): void {
    this.cache.clear();
    this.lastCacheUpdate = 0;
  }

  /**
   * Update a setting value with real-time broadcast
   */
  async updateSetting(category: string, key: string, value: any, updatedBy: string): Promise<void> {
    try {
      await executeQuery(
        `UPDATE system_settings
         SET setting_value = $3, updated_at = CURRENT_TIMESTAMP, updated_by = $4
         WHERE category = $1 AND setting_key = $2`,
        [category, key, value.toString(), updatedBy]
      );

      // Clear cache to force refresh
      this.clearCache();

      // REAL-TIME UPDATE: Broadcast to connected admin clients
      try {
        const { broadcastSettingsUpdate } = await import('../controllers/realtimeSettingsController');
        await broadcastSettingsUpdate(category, key, value);
      } catch (broadcastError) {
        // Don't fail the update if broadcast fails
        logger.warn('Failed to broadcast settings update:', broadcastError);
      }

      logger.info(`Setting updated with real-time broadcast: ${category}.${key}`, { value, updatedBy });
    } catch (error) {
      logger.error(`Failed to update setting ${category}.${key}:`, error);
      throw error;
    }
  }



  async getPaymentSettings() {
    logger.debug('Getting payment settings...');
    const settings = {
      minOrderAmount: await this.getSetting('payment', 'min_order_amount', 0.01),
      maxOrderAmount: await this.getSetting('payment', 'max_order_amount', 1000000),
      paymentTimeoutMinutes: await this.getSetting('payment', 'payment_timeout_minutes', 30),
      supportedCurrencies: await this.getSetting('payment', 'supported_currencies', ['TON', 'USD', 'EUR']),
    };
    logger.debug('Payment settings retrieved:', settings);
    return settings;
  }

  async getVoucherSettings() {
    return {
      defaultExpiryDays: await this.getSetting('voucher', 'default_expiry_days', 365),
      maxVouchersPerOrder: await this.getSetting('voucher', 'max_vouchers_per_order', 10),
      allowVoucherStacking: await this.getSetting('voucher', 'allow_voucher_stacking', false),
    };
  }

  async getUserManagementSettings() {
    return {
      allowRegistration: await this.getSetting('user_management', 'allow_registration', true),
      requireEmailVerification: await this.getSetting('user_management', 'require_email_verification', true),
      autoApproveUsers: await this.getSetting('user_management', 'auto_approve_users', true),
      defaultUserRole: await this.getSetting('user_management', 'default_user_role', 'user'),
    };
  }

  async getSystemSettings() {
    return {
      maintenanceMode: await this.getSetting('system', 'maintenance_mode', false),
      maintenanceMessage: await this.getSetting('system', 'maintenance_message', 'System is under maintenance. Please try again later.'),
      apiVersion: await this.getSetting('system', 'api_version', 'v1'),
      maxFileUploadSize: await this.getSetting('system', 'max_file_upload_size', 10485760),
      enableAnalytics: await this.getSetting('system', 'enable_analytics', true),
      logLevel: await this.getSetting('system', 'log_level', 'info'),
    };
  }

  async getEmailSettings() {
    logger.debug('Getting email settings...');
    const settings = {
      fromEmail: await this.getSetting('email', 'from_email', '<EMAIL>'),
      fromName: await this.getSetting('email', 'from_name', 'TON Voucher Platform'),
      smtpHost: await this.getSetting('email', 'smtp_host', 'smtp.gmail.com'),
      smtpPort: await this.getSetting('email', 'smtp_port', 587),
      smtpSecure: await this.getSetting('email', 'smtp_secure', false),
    };
    logger.debug('Email settings retrieved:', settings);
    return settings;
  }

  async getNotificationSettings() {
    logger.debug('Getting notification settings...');
    const settings = {
      enableEmailNotifications: await this.getSetting('notifications', 'enable_email_notifications', true),
      enableOrderConfirmations: await this.getSetting('notifications', 'enable_order_confirmations', true),
      enableVoucherNotifications: await this.getSetting('notifications', 'enable_voucher_notifications', true),
      enableSecurityAlerts: await this.getSetting('notifications', 'enable_security_alerts', true),
    };
    logger.debug('Notification settings retrieved:', settings);
    return settings;
  }

  async getPerformanceSettings() {
    logger.debug('Getting performance settings...');
    const settings = {
      cacheTtlSeconds: await this.getSetting('performance', 'cache_ttl_seconds', 3600),
      enableCompression: await this.getSetting('performance', 'enable_compression', true),
      maxConcurrentRequests: await this.getSetting('performance', 'max_concurrent_requests', 1000),
    };
    logger.debug('Performance settings retrieved:', settings);
    return settings;
  }

  async getBlockchainSettings() {
    logger.debug('Getting blockchain settings...');
    const settings = {
      tonNetwork: await this.getSetting('blockchain', 'ton_network', 'testnet'),
      tonApiEndpoint: await this.getSetting('blockchain', 'ton_api_endpoint', 'https://testnet.toncenter.com/api/v2/jsonRPC'),
      confirmationBlocks: await this.getSetting('blockchain', 'confirmation_blocks', 3),
      gasLimit: await this.getSetting('blockchain', 'gas_limit', '1000000'),
    };
    logger.debug('Blockchain settings retrieved:', settings);
    return settings;
  }

  async getDatabaseSettings() {
    logger.debug('Getting database settings...');
    const settings = {
      connectionPoolSize: await this.getSetting('database', 'connection_pool_size', 20),
      queryTimeoutSeconds: await this.getSetting('database', 'query_timeout_seconds', 30),
      enableQueryLogging: await this.getSetting('database', 'enable_query_logging', false),
    };
    logger.debug('Database settings retrieved:', settings);
    return settings;
  }

  async getRateLimitSettings() {
    logger.debug('Getting rate limit settings...');
    const settings = {
      // General rate limiting
      generalWindowMs: await this.getSetting('rate_limit', 'general_window_ms', 15 * 60 * 1000),
      generalRequestsPerWindow: await this.getSetting('rate_limit', 'general_requests_per_window', 100),

      // Authentication rate limiting
      authWindowMs: await this.getSetting('rate_limit', 'auth_window_ms', 15 * 60 * 1000),
      authRequestsPerWindow: await this.getSetting('rate_limit', 'auth_requests_per_window', 5),

      // Admin rate limiting
      adminWindowMs: await this.getSetting('rate_limit', 'admin_window_ms', 15 * 60 * 1000),
      adminRequestsPerWindow: await this.getSetting('rate_limit', 'admin_requests_per_window', 500),

      // Order rate limiting
      orderWindowMs: await this.getSetting('rate_limit', 'order_window_ms', 15 * 60 * 1000),
      orderRequestsPerWindow: await this.getSetting('rate_limit', 'order_requests_per_window', 5),

      // Payment verification rate limiting
      paymentVerifyWindowMs: await this.getSetting('rate_limit', 'payment_verify_window_ms', 5 * 60 * 1000),
      paymentVerifyRequestsPerWindow: await this.getSetting('rate_limit', 'payment_verify_requests_per_window', 20),

      // Voucher redemption rate limiting
      voucherRedeemWindowMs: await this.getSetting('rate_limit', 'voucher_redeem_window_ms', 60 * 1000),
      voucherRedeemRequestsPerWindow: await this.getSetting('rate_limit', 'voucher_redeem_requests_per_window', 10),

      // Password reset rate limiting
      passwordResetWindowMs: await this.getSetting('rate_limit', 'password_reset_window_ms', 60 * 60 * 1000),
      passwordResetRequestsPerWindow: await this.getSetting('rate_limit', 'password_reset_requests_per_window', 3),

      // Speed limiter settings
      speedLimiterDelayAfter: await this.getSetting('rate_limit', 'speed_limiter_delay_after', 50),
      speedLimiterDelayMs: await this.getSetting('rate_limit', 'speed_limiter_delay_ms', 500),
      speedLimiterMaxDelayMs: await this.getSetting('rate_limit', 'speed_limiter_max_delay_ms', 20000),
    };
    logger.debug('Rate limit settings retrieved:', settings);
    return settings;
  }

  async getSecuritySettings() {
    logger.debug('Getting security settings...');
    const settings = {
      // JWT settings
      jwtAccessExpiresIn: await this.getSetting('security', 'jwt_access_expires_in', '15m'),
      jwtRefreshExpiresIn: await this.getSetting('security', 'jwt_refresh_expires_in', '7d'),

      // Password settings
      bcryptRounds: await this.getSetting('security', 'bcrypt_rounds', 12),
      passwordMinLength: await this.getSetting('security', 'password_min_length', 8),
      passwordRequireLowercase: await this.getSetting('security', 'password_require_lowercase', true),
      passwordRequireUppercase: await this.getSetting('security', 'password_require_uppercase', true),
      passwordRequireNumbers: await this.getSetting('security', 'password_require_numbers', true),
      passwordRequireSymbols: await this.getSetting('security', 'password_require_symbols', true),

      // Session settings
      sessionMaxAge: await this.getSetting('security', 'session_max_age', 24 * 60 * 60 * 1000),
      sessionTimeoutMinutes: await this.getSetting('security', 'session_timeout_minutes', 60),

      // Account lockout settings
      maxLoginAttempts: await this.getSetting('security', 'max_login_attempts', 5),
      lockoutDuration: await this.getSetting('security', 'lockout_duration', 30 * 60 * 1000),
      lockoutDurationMinutes: await this.getSetting('security', 'lockout_duration_minutes', 30),

      // Two-factor authentication
      enableTwoFactor: await this.getSetting('security', 'enable_two_factor', false),

      // CSRF settings
      csrfCookieName: await this.getSetting('security', 'csrf_cookie_name', 'csrf-token'),
    };
    logger.debug('Security settings retrieved:', settings);
    return settings;
  }

  async getBusinessRuleSettings() {
    logger.debug('Getting business rule settings...');
    const settings = {
      // Voucher settings
      voucherCodeLength: await this.getSetting('business_rules', 'voucher_code_length', 12),
      voucherExpiryDays: await this.getSetting('business_rules', 'voucher_expiry_days', 365),
      maxVouchersPerOrder: await this.getSetting('business_rules', 'max_vouchers_per_order', 10),

      // Order settings
      minOrderAmount: await this.getSetting('business_rules', 'min_order_amount', '0.01'),
      maxOrderAmount: await this.getSetting('business_rules', 'max_order_amount', '1000000'),
      orderTimeoutMinutes: await this.getSetting('business_rules', 'order_timeout_minutes', 30),

      // Payment settings
      paymentConfirmationBlocks: await this.getSetting('business_rules', 'payment_confirmation_blocks', 3),
      paymentTimeoutMinutes: await this.getSetting('business_rules', 'payment_timeout_minutes', 30),

      // Email verification settings
      emailVerificationExpiresHours: await this.getSetting('business_rules', 'email_verification_expires_hours', 24),
      passwordResetExpiresHours: await this.getSetting('business_rules', 'password_reset_expires_hours', 1),
    };
    logger.debug('Business rule settings retrieved:', settings);
    return settings;
  }

  async getFileUploadSettings() {
    logger.debug('Getting file upload settings...');
    const settings = {
      maxFileSize: await this.getSetting('file_upload', 'max_file_size', 5 * 1024 * 1024),
      uploadPath: await this.getSetting('file_upload', 'upload_path', '/uploads'),
      allowedMimeTypes: await this.getSetting('file_upload', 'allowed_mime_types', ['image/jpeg', 'image/png', 'image/webp']),
    };
    logger.debug('File upload settings retrieved:', settings);
    return settings;
  }

  async getPaginationSettings() {
    logger.debug('Getting pagination settings...');
    const settings = {
      defaultPage: await this.getSetting('pagination', 'default_page', 1),
      defaultLimit: await this.getSetting('pagination', 'default_limit', 20),
      maxLimit: await this.getSetting('pagination', 'max_limit', 100),
    };
    logger.debug('Pagination settings retrieved:', settings);
    return settings;
  }

  async getCacheSettings() {
    logger.debug('Getting cache settings...');
    const settings = {
      userSessionTtl: await this.getSetting('cache', 'user_session_ttl', 60 * 60),
      voucherStatusTtl: await this.getSetting('cache', 'voucher_status_ttl', 5 * 60),
      orderStatusTtl: await this.getSetting('cache', 'order_status_ttl', 2 * 60),
      paymentStatusTtl: await this.getSetting('cache', 'payment_status_ttl', 30),
      adminStatsTtl: await this.getSetting('cache', 'admin_stats_ttl', 10 * 60),
      settingsCacheTtl: await this.getSetting('cache', 'settings_cache_ttl', 5 * 60),
    };
    logger.debug('Cache settings retrieved:', settings);
    return settings;
  }

  async getMiddlewareSettings() {
    logger.debug('Getting middleware settings...');
    const settings = {
      performanceMonitoringThreshold: await this.getSetting('middleware', 'performance_monitoring_threshold', 2000),
      requestSizeMonitoringLimit: await this.getSetting('middleware', 'request_size_monitoring_limit', 5 * 1024 * 1024),
      requestSizeLimit: await this.getSetting('middleware', 'request_size_limit', 10 * 1024 * 1024),
      jsonBodyLimit: await this.getSetting('middleware', 'json_body_limit', 10 * 1024 * 1024),
      memoMaxLength: await this.getSetting('middleware', 'memo_max_length', 500),
    };
    logger.debug('Middleware settings retrieved:', settings);
    return settings;
  }

}

// Export singleton instance
export const settingsService = new SettingsService();

// Export convenience functions
export const getSetting = (category: string, key: string, defaultValue?: any) => 
  settingsService.getSetting(category, key, defaultValue);

export const getSettingsByCategory = (category: string) =>
  settingsService.getSettingsByCategory(category);

export const getNotificationSettings = () =>
  settingsService.getNotificationSettings();

export const getPerformanceSettings = () =>
  settingsService.getPerformanceSettings();

export const getBlockchainSettings = () =>
  settingsService.getBlockchainSettings();

export const getDatabaseSettings = () =>
  settingsService.getDatabaseSettings();

export const getPaymentSettings = () =>
  settingsService.getPaymentSettings();

export const getVoucherSettings = () =>
  settingsService.getVoucherSettings();

export const getEmailSettings = () =>
  settingsService.getEmailSettings();

export const getSystemSettings = () =>
  settingsService.getSystemSettings();

export const getSecuritySettings = () =>
  settingsService.getSecuritySettings();

export const getUserManagementSettings = () =>
  settingsService.getUserManagementSettings();

export const getRateLimitSettings = () =>
  settingsService.getRateLimitSettings();

export const getAllSettings = () =>
  settingsService.getAllSettings();

export const updateSetting = (category: string, key: string, value: any, updatedBy: string) =>
  settingsService.updateSetting(category, key, value, updatedBy);

export const clearSettingsCache = () =>
  settingsService.clearCache();

export const getBusinessRuleSettings = () =>
  settingsService.getBusinessRuleSettings();

export const getFileUploadSettings = () =>
  settingsService.getFileUploadSettings();

export const getPaginationSettings = () =>
  settingsService.getPaginationSettings();

export const getCacheSettings = () =>
  settingsService.getCacheSettings();

export const getMiddlewareSettings = () =>
  settingsService.getMiddlewareSettings();
