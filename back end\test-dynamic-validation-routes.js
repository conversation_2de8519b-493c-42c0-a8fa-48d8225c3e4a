/**
 * Test script for dynamic validation in routes
 * 
 * This script tests that the routes are properly using dynamic validation
 * by checking the validation middleware setup.
 */

const fs = require('fs');
const path = require('path');

function testDynamicValidationRoutes() {
  console.log('🧪 Testing Dynamic Validation Routes Integration...\n');

  try {
    // Test 1: Check admin routes for dynamic validation usage
    console.log('1. Checking admin routes for dynamic validation...');
    const adminRoutesPath = path.join(__dirname, 'src', 'routes', 'admin.ts');
    const adminRoutesContent = fs.readFileSync(adminRoutesPath, 'utf8');

    // Check for dynamic validation imports
    if (adminRoutesContent.includes('dynamicValidationMiddleware')) {
      console.log('✅ Admin routes import dynamic validation middleware');
    } else {
      console.log('❌ Admin routes missing dynamic validation middleware import');
      return false;
    }

    // Check for dynamic validation usage in voucher creation
    if (adminRoutesContent.includes('createVoucherValidationDynamic')) {
      console.log('✅ Admin routes use dynamic voucher validation');
    } else {
      console.log('❌ Admin routes missing dynamic voucher validation');
      return false;
    }

    // Check for dynamic validation usage in product creation
    if (adminRoutesContent.includes('createProductValidationDynamic')) {
      console.log('✅ Admin routes use dynamic product validation');
    } else {
      console.log('❌ Admin routes missing dynamic product validation');
      return false;
    }

    // Test 2: Check orders routes for dynamic validation usage
    console.log('\n2. Checking orders routes for dynamic validation...');
    const orderRoutesPath = path.join(__dirname, 'src', 'routes', 'orders.ts');
    const orderRoutesContent = fs.readFileSync(orderRoutesPath, 'utf8');

    if (orderRoutesContent.includes('getCachedCurrencyValidation')) {
      console.log('✅ Order routes use dynamic currency validation');
    } else {
      console.log('❌ Order routes missing dynamic currency validation');
      return false;
    }

    if (orderRoutesContent.includes('createOrderValidationDynamic')) {
      console.log('✅ Order routes use dynamic order validation');
    } else {
      console.log('❌ Order routes missing dynamic order validation');
      return false;
    }

    // Test 3: Check dynamic validation middleware exists
    console.log('\n3. Checking dynamic validation middleware...');
    const dynamicValidationPath = path.join(__dirname, 'src', 'middleware', 'dynamicValidation.ts');
    
    if (fs.existsSync(dynamicValidationPath)) {
      console.log('✅ Dynamic validation middleware file exists');
      
      const dynamicValidationContent = fs.readFileSync(dynamicValidationPath, 'utf8');
      
      // Check for key functions
      const requiredFunctions = [
        'createCurrencyValidation',
        'createAmountValidation',
        'createVoucherQuantityValidation',
        'createExpiryDaysValidation',
        'dynamicValidationMiddleware',
        'getCachedCurrencyValidation'
      ];

      let allFunctionsPresent = true;
      for (const func of requiredFunctions) {
        if (dynamicValidationContent.includes(func)) {
          console.log(`✅ Function ${func} present`);
        } else {
          console.log(`❌ Function ${func} missing`);
          allFunctionsPresent = false;
        }
      }

      if (!allFunctionsPresent) {
        return false;
      }
    } else {
      console.log('❌ Dynamic validation middleware file missing');
      return false;
    }

    // Test 4: Check that hardcoded values are replaced
    console.log('\n4. Checking for removal of hardcoded validation values...');
    
    // Check admin routes for hardcoded currencies
    const hardcodedCurrencyPattern = /isIn\(\s*\[\s*['"]TON['"],\s*['"]USD['"],\s*['"]EUR['"]\s*\]\s*\)/;
    if (hardcodedCurrencyPattern.test(adminRoutesContent)) {
      console.log('❌ Admin routes still contain hardcoded currency validation');
      return false;
    } else {
      console.log('✅ Admin routes no longer use hardcoded currency validation');
    }

    // Check orders routes for hardcoded currencies
    if (hardcodedCurrencyPattern.test(orderRoutesContent)) {
      console.log('❌ Order routes still contain hardcoded currency validation');
      return false;
    } else {
      console.log('✅ Order routes no longer use hardcoded currency validation');
    }

    // Test 5: Check settings service integration
    console.log('\n5. Checking settings service integration...');
    if (fs.existsSync(dynamicValidationPath)) {
      const dynamicValidationContent = fs.readFileSync(dynamicValidationPath, 'utf8');

      if (dynamicValidationContent.includes('getPaymentSettings')) {
        console.log('✅ Dynamic validation uses payment settings');
      } else {
        console.log('❌ Dynamic validation missing payment settings integration');
        return false;
      }

      if (dynamicValidationContent.includes('getVoucherSettings')) {
        console.log('✅ Dynamic validation uses voucher settings');
      } else {
        console.log('❌ Dynamic validation missing voucher settings integration');
        return false;
      }
    } else {
      console.log('❌ Dynamic validation middleware file missing');
      return false;
    }

    console.log('\n✅ Dynamic Validation Routes Integration Test PASSED');
    console.log('   Backend validation now uses dynamic settings from the database');
    console.log('   instead of hardcoded values for currencies, limits, and constraints.');
    
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
const success = testDynamicValidationRoutes();

if (success) {
  console.log('\n🎉 All tests passed! Dynamic validation routes are properly configured.');
  process.exit(0);
} else {
  console.log('\n❌ Tests failed. Please fix the issues before proceeding.');
  process.exit(1);
}
