{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "noEmitOnError": true, "forceConsistentCasingInFileNames": true, "declaration": false, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": false, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/config/*": ["config/*"], "@/controllers/*": ["controllers/*"], "@/middleware/*": ["middleware/*"], "@/models/*": ["models/*"], "@/routes/*": ["routes/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}