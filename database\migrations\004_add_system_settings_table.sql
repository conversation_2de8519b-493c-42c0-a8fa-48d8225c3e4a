-- Migration: Add system settings table
-- Description: Create system_settings table for storing admin-configurable application settings

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(50) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    data_type VARCHAR(20) DEFAULT 'string' CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_readonly BOOLEAN DEFAULT FALSE,
    validation_rules JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(id),
    
    -- Constraints
    UNIQUE(category, setting_key)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_system_settings_updated_at ON system_settings(updated_at);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS trigger_system_settings_updated_at ON system_settings;
CREATE TRIGGER trigger_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_updated_at();

-- Insert default system settings
INSERT INTO system_settings (category, setting_key, setting_value, data_type, description, is_sensitive, is_readonly) VALUES
-- Security Settings
('security', 'max_login_attempts', '5', 'number', 'Maximum failed login attempts before account lockout', false, false),
('security', 'lockout_duration_minutes', '30', 'number', 'Account lockout duration in minutes', false, false),
('security', 'password_min_length', '8', 'number', 'Minimum password length requirement', false, false),
('security', 'password_require_uppercase', 'true', 'boolean', 'Require uppercase letters in passwords', false, false),
('security', 'password_require_lowercase', 'true', 'boolean', 'Require lowercase letters in passwords', false, false),
('security', 'password_require_numbers', 'true', 'boolean', 'Require numbers in passwords', false, false),
('security', 'password_require_symbols', 'true', 'boolean', 'Require special characters in passwords', false, false),
('security', 'session_timeout_minutes', '1440', 'number', 'User session timeout in minutes (24 hours)', false, false),
('security', 'enable_two_factor', 'false', 'boolean', 'Enable two-factor authentication requirement', false, false),

-- Rate Limiting Settings
('rate_limiting', 'general_requests_per_window', '100', 'number', 'General API requests per 15-minute window', false, false),
('rate_limiting', 'admin_requests_per_window', '500', 'number', 'Admin API requests per 15-minute window', false, false),
('rate_limiting', 'order_requests_per_window', '10', 'number', 'Order creation requests per 15-minute window', false, false),
('rate_limiting', 'auth_requests_per_window', '5', 'number', 'Authentication requests per 15-minute window', false, false),

-- User Management Settings
('user_management', 'allow_registration', 'true', 'boolean', 'Allow new user registration', false, false),
('user_management', 'require_email_verification', 'true', 'boolean', 'Require email verification for new accounts', false, false),
('user_management', 'auto_approve_users', 'true', 'boolean', 'Automatically approve new user accounts', false, false),
('user_management', 'default_user_role', 'user', 'string', 'Default role for new users', false, false),

-- Payment and Voucher Settings
('payment', 'min_order_amount', '0.01', 'number', 'Minimum order amount in TON', false, false),
('payment', 'max_order_amount', '1000000', 'number', 'Maximum order amount in TON', false, false),
('payment', 'payment_timeout_minutes', '30', 'number', 'Payment timeout in minutes', false, false),
('payment', 'supported_currencies', '["TON", "USD", "EUR"]', 'json', 'List of supported currencies', false, false),

('voucher', 'default_expiry_days', '365', 'number', 'Default voucher expiry in days', false, false),
('voucher', 'max_vouchers_per_order', '10', 'number', 'Maximum vouchers per single order', false, false),
('voucher', 'allow_voucher_stacking', 'false', 'boolean', 'Allow multiple vouchers per order', false, false),

-- Email and Notification Settings
('email', 'smtp_host', 'smtp.gmail.com', 'string', 'SMTP server hostname', false, false),
('email', 'smtp_port', '587', 'number', 'SMTP server port', false, false),
('email', 'smtp_secure', 'false', 'boolean', 'Use secure SMTP connection', false, false),
('email', 'from_email', '<EMAIL>', 'string', 'Default from email address', false, false),
('email', 'from_name', 'TON Voucher Platform', 'string', 'Default from name', false, false),

('notifications', 'enable_email_notifications', 'true', 'boolean', 'Enable email notifications', false, false),
('notifications', 'enable_order_confirmations', 'true', 'boolean', 'Send order confirmation emails', false, false),
('notifications', 'enable_voucher_notifications', 'true', 'boolean', 'Send voucher delivery emails', false, false),
('notifications', 'enable_security_alerts', 'true', 'boolean', 'Send security alert emails', false, false),

-- System Configuration
('system', 'maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', false, false),
('system', 'maintenance_message', 'System is under maintenance. Please try again later.', 'string', 'Maintenance mode message', false, false),
('system', 'api_version', 'v1', 'string', 'Current API version', false, true),
('system', 'max_file_upload_size', '10485760', 'number', 'Maximum file upload size in bytes (10MB)', false, false),
('system', 'enable_analytics', 'true', 'boolean', 'Enable system analytics', false, false),
('system', 'log_level', 'info', 'string', 'System logging level', false, false),

-- TON Blockchain Settings
('blockchain', 'ton_network', 'testnet', 'string', 'TON network (mainnet/testnet)', false, false),
('blockchain', 'ton_api_endpoint', 'https://testnet.toncenter.com/api/v2/jsonRPC', 'string', 'TON API endpoint URL', false, false),
('blockchain', 'confirmation_blocks', '3', 'number', 'Required confirmation blocks', false, false),
('blockchain', 'gas_limit', '1000000', 'string', 'Default gas limit for transactions', false, false),

-- Database and Performance Settings
('database', 'connection_pool_size', '20', 'number', 'Database connection pool size', false, false),
('database', 'query_timeout_seconds', '30', 'number', 'Database query timeout in seconds', false, false),
('database', 'enable_query_logging', 'false', 'boolean', 'Enable database query logging', false, false),

('performance', 'cache_ttl_seconds', '3600', 'number', 'Default cache TTL in seconds', false, false),
('performance', 'enable_compression', 'true', 'boolean', 'Enable response compression', false, false),
('performance', 'max_concurrent_requests', '1000', 'number', 'Maximum concurrent requests', false, false)

ON CONFLICT (category, setting_key) DO NOTHING;

-- Create settings change log table for audit trail
CREATE TABLE IF NOT EXISTS system_settings_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_id UUID NOT NULL REFERENCES system_settings(id),
    category VARCHAR(50) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT NOT NULL,
    changed_by UUID NOT NULL REFERENCES users(id),
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for settings log
CREATE INDEX IF NOT EXISTS idx_system_settings_log_setting_id ON system_settings_log(setting_id);
CREATE INDEX IF NOT EXISTS idx_system_settings_log_changed_by ON system_settings_log(changed_by);
CREATE INDEX IF NOT EXISTS idx_system_settings_log_created_at ON system_settings_log(created_at);
CREATE INDEX IF NOT EXISTS idx_system_settings_log_category ON system_settings_log(category);
