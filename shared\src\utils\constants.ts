// Application constants

// API Configuration
export const API_ENDPOINTS = {
  // Authentication
  AUTH_REGISTER: '/api/auth/register',
  AUTH_LOGIN: '/api/auth/login',
  AUTH_LOGOUT: '/api/auth/logout',
  AUTH_REFRESH: '/api/auth/refresh',
  AUTH_VERIFY_EMAIL: '/api/auth/verify-email',
  AUTH_RESET_PASSWORD: '/api/auth/reset-password',
  
  // User Management
  USER_PROFILE: '/api/user/profile',
  USER_UPDATE: '/api/user/update',
  USER_ORDERS: '/api/user/orders',
  USER_VOUCHERS: '/api/user/vouchers',
  
  // Orders
  ORDERS_CREATE: '/api/orders',
  ORDERS_LIST: '/api/orders',
  ORDERS_DETAIL: '/api/orders/:id',
  ORDERS_STATUS: '/api/orders/:id/status',
  
  // Payments
  PAYMENTS_INITIATE: '/api/payments/initiate',
  PAYMENTS_VERIFY: '/api/payments/verify',
  PAYMENTS_STATUS: '/api/payments/:id/status',
  PAYMENTS_WEBHOOK: '/api/payments/webhook',
  
  // Vouchers
  VOUCHERS_GENERATE: '/api/vouchers/generate',
  VOUCHERS_LIST: '/api/vouchers',
  VOUCHERS_REDEEM: '/api/vouchers/redeem',
  VOUCHERS_STATUS: '/api/vouchers/:code/status',
  
  // Admin
  ADMIN_USERS: '/api/admin/users',
  ADMIN_ORDERS: '/api/admin/orders',
  ADMIN_VOUCHERS: '/api/admin/vouchers',
  ADMIN_STATS: '/api/admin/stats',
  ADMIN_BULK_VOUCHERS: '/api/admin/vouchers/bulk',
} as const;

// Rate Limiting
export const RATE_LIMITS = {
  // Authentication endpoints
  AUTH_REGISTER: { windowMs: 15 * 60 * 1000, max: 5 }, // 5 attempts per 15 minutes
  AUTH_LOGIN: { windowMs: 15 * 60 * 1000, max: 10 }, // 10 attempts per 15 minutes
  AUTH_RESET_PASSWORD: { windowMs: 60 * 60 * 1000, max: 3 }, // 3 attempts per hour
  
  // Order creation
  ORDER_CREATE: { windowMs: 15 * 60 * 1000, max: 5 }, // 5 orders per 15 minutes
  
  // Payment verification
  PAYMENT_VERIFY: { windowMs: 5 * 60 * 1000, max: 20 }, // 20 verifications per 5 minutes
  
  // Voucher redemption
  VOUCHER_REDEEM: { windowMs: 60 * 1000, max: 10 }, // 10 redemptions per minute
  
  // General API
  GENERAL: { windowMs: 15 * 60 * 1000, max: 100 }, // 100 requests per 15 minutes
} as const;

// Security Configuration
export const SECURITY_CONFIG = {
  // JWT
  JWT_ACCESS_EXPIRES_IN: '15m',
  JWT_REFRESH_EXPIRES_IN: '7d',
  
  // Password
  BCRYPT_ROUNDS: 12,
  
  // Session
  SESSION_MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours
  
  // CSRF
  CSRF_COOKIE_NAME: 'csrf-token',
  
  // Account lockout
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 30 * 60 * 1000, // 30 minutes
} as const;

// Business Logic Constants
export const BUSINESS_RULES = {
  // Voucher
  VOUCHER_CODE_LENGTH: 12,
  VOUCHER_EXPIRY_DAYS: 365,
  
  // Order
  MIN_ORDER_AMOUNT: '0.01',
  MAX_ORDER_AMOUNT: '1000000',
  ORDER_TIMEOUT_MINUTES: 30,
  
  // Payment
  PAYMENT_CONFIRMATION_BLOCKS: 3,
  PAYMENT_TIMEOUT_MINUTES: 30,
  
  // Email
  EMAIL_VERIFICATION_EXPIRES_HOURS: 24,
  PASSWORD_RESET_EXPIRES_HOURS: 1,
} as const;

// TON Configuration
export const TON_CONFIG = {
  // Networks
  MAINNET_ENDPOINT: 'https://toncenter.com/api/v2/jsonRPC',
  TESTNET_ENDPOINT: 'https://testnet.toncenter.com/api/v2/jsonRPC',
  
  // Wallet types
  SUPPORTED_WALLETS: ['tonkeeper', 'openmask', 'tonhub'] as const,
  
  // Transaction
  DEFAULT_GAS_LIMIT: '1000000',
  MIN_CONFIRMATION_BLOCKS: 3,
} as const;

// Email Templates
export const EMAIL_TEMPLATES = {
  WELCOME: 'welcome',
  EMAIL_VERIFICATION: 'email-verification',
  PASSWORD_RESET: 'password-reset',
  ORDER_CONFIRMATION: 'order-confirmation',
  VOUCHER_DELIVERY: 'voucher-delivery',
  PAYMENT_RECEIVED: 'payment-received',
} as const;

// File Upload
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_MIME_TYPES: ['image/jpeg', 'image/png', 'image/webp'] as const,
  UPLOAD_PATH: '/uploads',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// Cache TTL (Time To Live)
export const CACHE_TTL = {
  USER_SESSION: 60 * 60, // 1 hour
  VOUCHER_STATUS: 5 * 60, // 5 minutes
  ORDER_STATUS: 2 * 60, // 2 minutes
  PAYMENT_STATUS: 30, // 30 seconds
  ADMIN_STATS: 10 * 60, // 10 minutes
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  EMAIL_NOT_VERIFIED: 'Please verify your email address',
  ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed attempts',
  TOKEN_EXPIRED: 'Token has expired',
  TOKEN_INVALID: 'Invalid token',
  
  // Authorization
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  ADMIN_REQUIRED: 'Admin access required',
  
  // Validation
  INVALID_INPUT: 'Invalid input data',
  REQUIRED_FIELD: 'This field is required',
  EMAIL_EXISTS: 'Email address already exists',
  TELEGRAM_ID_EXISTS: 'Telegram ID already exists',
  
  // Orders
  ORDER_NOT_FOUND: 'Order not found',
  ORDER_ALREADY_PAID: 'Order has already been paid',
  ORDER_EXPIRED: 'Order has expired',
  
  // Payments
  PAYMENT_FAILED: 'Payment failed',
  PAYMENT_TIMEOUT: 'Payment timeout',
  INSUFFICIENT_AMOUNT: 'Insufficient payment amount',
  
  // Vouchers
  VOUCHER_NOT_FOUND: 'Voucher not found',
  VOUCHER_ALREADY_REDEEMED: 'Voucher has already been redeemed',
  VOUCHER_EXPIRED: 'Voucher has expired',
  
  // General
  INTERNAL_ERROR: 'Internal server error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  RATE_LIMIT_EXCEEDED: 'Too many requests, please try again later',
} as const;
