import { useSettings } from '../contexts/SettingsContext';

/**
 * Custom hook for accessing performance settings
 * Provides easy access to performance settings and helper functions
 */
export const usePerformance = () => {
  const { getPerformanceSettings } = useSettings();
  const performanceSettings = getPerformanceSettings();

  // Helper functions to get specific performance settings
  const getCacheTtl = () => {
    return performanceSettings?.cacheTtlSeconds ?? 3600;
  };

  const isCompressionEnabled = () => {
    return performanceSettings?.enableCompression ?? true;
  };

  const getMaxConcurrentRequests = () => {
    return performanceSettings?.maxConcurrentRequests ?? 1000;
  };

  // Helper function to calculate cache expiry date
  const getCacheExpiryDate = (fromDate = new Date()) => {
    const ttlSeconds = getCacheTtl();
    const expiryDate = new Date(fromDate);
    expiryDate.setSeconds(expiryDate.getSeconds() + ttlSeconds);
    return expiryDate;
  };

  // Helper function to check if a cached item is still valid
  const isCacheValid = (cachedAt: Date) => {
    const ttlSeconds = getCacheTtl();
    const now = new Date();
    const expiryDate = new Date(cachedAt);
    expiryDate.setSeconds(expiryDate.getSeconds() + ttlSeconds);
    return now < expiryDate;
  };

  return {
    performanceSettings,
    getCacheTtl,
    isCompressionEnabled,
    getMaxConcurrentRequests,
    getCacheExpiryDate,
    isCacheValid,
  };
};
