import { Router } from 'express';
import {
  getUserSettings,
  updateUserSettings,
  exportUserData,
  deleteUserAccount,
} from '../controllers/settingsController';
import { validateInput } from '../middleware/security';
import { authenticate, requireUser } from '../middleware/auth';
import { body } from 'express-validator';

const router = Router();

// All settings routes require authentication
router.use(authenticate);
router.use(requireUser);

// Settings validation
const updateSettingsValidation = [
  body('notifications.email')
    .isBoolean()
    .withMessage('Email notifications must be a boolean'),
  body('notifications.push')
    .isBoolean()
    .withMessage('Push notifications must be a boolean'),
  body('notifications.orderUpdates')
    .isBoolean()
    .withMessage('Order updates notifications must be a boolean'),
  body('notifications.promotions')
    .isBoolean()
    .withMessage('Promotions notifications must be a boolean'),
  body('notifications.security')
    .isBoolean()
    .withMessage('Security notifications must be a boolean'),
  body('privacy.profileVisibility')
    .isIn(['public', 'private'])
    .withMessage('Profile visibility must be public or private'),
  body('privacy.dataSharing')
    .isBoolean()
    .withMessage('Data sharing must be a boolean'),
  body('privacy.analytics')
    .isBoolean()
    .withMessage('Analytics must be a boolean'),
  body('appearance.theme')
    .isIn(['light', 'dark', 'system'])
    .withMessage('Theme must be light, dark, or system'),
  body('appearance.language')
    .isLength({ min: 2, max: 10 })
    .withMessage('Language must be 2-10 characters'),
];

/**
 * @route   GET /api/v1/settings
 * @desc    Get user settings
 * @access  Private (User)
 */
router.get('/', getUserSettings);

/**
 * @route   PUT /api/v1/settings
 * @desc    Update user settings
 * @access  Private (User)
 */
router.put(
  '/',
  validateInput(updateSettingsValidation),
  updateUserSettings
);

/**
 * @route   GET /api/v1/settings/export
 * @desc    Export user data
 * @access  Private (User)
 */
router.get('/export', exportUserData);

/**
 * @route   DELETE /api/v1/settings/account
 * @desc    Delete user account
 * @access  Private (User)
 */
router.delete('/account', deleteUserAccount);

export default router;
