'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ShoppingCart,
  Gift,
  Star,
  Filter,
  Search,
  Grid,
  List,
  TrendingUp,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface VoucherProduct {
  id: string;
  name: string;
  description: string;
  category: string;
  minAmount: number;
  maxAmount: number;
  currency: string;
  rating: number;
  reviewCount: number;
  image: string;
  popular: boolean;
  available: boolean;
  features: string[];
}

const voucherProducts: VoucherProduct[] = [
  {
    id: 'steam',
    name: 'Steam Gift Card',
    description: 'Digital gift cards for the world\'s largest gaming platform. Access thousands of games, DLCs, and software.',
    category: 'Gaming',
    minAmount: 5,
    maxAmount: 500,
    currency: 'TON',
    rating: 4.9,
    reviewCount: 1250,
    image: '/images/steam-card.jpg',
    popular: true,
    available: true,
    features: ['Instant delivery', 'No expiration', 'Global redemption', '24/7 support'],
  },
  {
    id: 'playstation',
    name: 'PlayStation Store',
    description: 'PlayStation Network gift cards for games, add-ons, and PlayStation Plus subscriptions.',
    category: 'Gaming',
    minAmount: 10,
    maxAmount: 200,
    currency: 'TON',
    rating: 4.8,
    reviewCount: 890,
    image: '/images/psn-card.jpg',
    popular: true,
    available: true,
    features: ['Instant delivery', 'PS4 & PS5 compatible', 'Digital content', 'Secure payment'],
  },
  {
    id: 'xbox',
    name: 'Xbox Game Pass',
    description: 'Xbox Game Pass subscription codes for unlimited access to hundreds of games.',
    category: 'Gaming',
    minAmount: 15,
    maxAmount: 100,
    currency: 'TON',
    rating: 4.7,
    reviewCount: 650,
    image: '/images/xbox-card.jpg',
    popular: false,
    available: false,
    features: ['Coming soon', 'Game Pass Ultimate', 'PC & Console', 'Cloud gaming'],
  },
  {
    id: 'nintendo',
    name: 'Nintendo eShop',
    description: 'Nintendo eShop gift cards for Switch games, DLCs, and digital content.',
    category: 'Gaming',
    minAmount: 10,
    maxAmount: 300,
    currency: 'TON',
    rating: 4.8,
    reviewCount: 720,
    image: '/images/nintendo-card.jpg',
    popular: false,
    available: true,
    features: ['Instant delivery', 'Switch compatible', 'Digital games', 'Family friendly'],
  },
];

const categories = ['All', 'Gaming', 'Entertainment', 'Software'];

export default function ShopPage() {
  const { user } = useAuth();
  const [products, setProducts] = useState<VoucherProduct[]>(voucherProducts);
  const [loading, setLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popular');

  useEffect(() => {
    filterProducts();
  }, [selectedCategory, searchTerm, sortBy]);

  const filterProducts = () => {
    let filtered = voucherProducts;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort products
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => (b.popular ? 1 : 0) - (a.popular ? 1 : 0));
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'price-low':
        filtered.sort((a, b) => a.minAmount - b.minAmount);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.minAmount - a.minAmount);
        break;
    }

    setProducts(filtered);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const ProductCard = ({ product }: { product: VoucherProduct }) => (
    <div className={`card group ${!product.available ? 'opacity-60' : ''}`}>
      <div className="relative">
        {product.popular && (
          <div className="absolute top-2 left-2 z-10">
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-ton-600 text-white rounded-full">
              <TrendingUp className="h-3 w-3 mr-1" />
              Popular
            </span>
          </div>
        )}
        {!product.available && (
          <div className="absolute inset-0 bg-gray-900 bg-opacity-50 rounded-t-lg flex items-center justify-center z-10">
            <span className="text-white font-medium">Coming Soon</span>
          </div>
        )}
        <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg flex items-center justify-center">
          <Gift className="h-16 w-16 text-gray-400" />
        </div>
      </div>
      <div className="card-body">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 group-hover:text-ton-600 transition-colors">
            {product.name}
          </h3>
          <div className="flex items-center gap-1">
            {renderStars(product.rating)}
            <span className="text-xs text-gray-500 ml-1">({product.reviewCount})</span>
          </div>
        </div>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
        <div className="flex items-center justify-between mb-3">
          <div className="text-sm">
            <span className="text-gray-600">From </span>
            <span className="font-bold text-ton-600">
              {product.minAmount} {product.currency}
            </span>
          </div>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {product.category}
          </span>
        </div>
        <div className="space-y-2 mb-4">
          {product.features.slice(0, 2).map((feature, index) => (
            <div key={index} className="flex items-center text-xs text-gray-600">
              <div className="w-1 h-1 bg-ton-600 rounded-full mr-2" />
              {feature}
            </div>
          ))}
        </div>
        <Link
          href={product.available ? `/dashboard/orders/new?type=${product.id}` : '#'}
          className={`btn-primary w-full inline-flex items-center justify-center ${
            !product.available ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={(e) => !product.available && e.preventDefault()}
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          {product.available ? 'Purchase' : 'Coming Soon'}
        </Link>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Voucher Shop</h1>
            <p className="mt-2 text-lg text-gray-600">
              Premium gaming vouchers with secure TON payments
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search vouchers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="input"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input"
            >
              <option value="popular">Most Popular</option>
              <option value="rating">Highest Rated</option>
              <option value="name">Name A-Z</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
            </select>

            {/* View Mode */}
            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-ton-600 text-white' : 'text-gray-600'}`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-ton-600 text-white' : 'text-gray-600'}`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {products.length} {products.length === 1 ? 'voucher' : 'vouchers'} found
          </h2>
          {!user && (
            <div className="text-sm text-gray-600">
              <Link href="/auth/login" className="text-ton-600 hover:text-ton-500">
                Sign in
              </Link>
              {' '}to purchase vouchers
            </div>
          )}
        </div>

        {/* Products Grid */}
        {products.length > 0 ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {products.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Gift className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No vouchers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}

        {/* Call to Action */}
        {!user && (
          <div className="mt-12 bg-gradient-to-r from-ton-600 to-ton-700 rounded-lg p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-2">Ready to get started?</h3>
            <p className="text-ton-100 mb-6">
              Create an account to purchase premium vouchers with TON cryptocurrency
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register" className="btn bg-white text-ton-600 hover:bg-gray-100">
                Create Account
              </Link>
              <Link href="/auth/login" className="btn border-white text-white hover:bg-ton-500">
                Sign In
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
