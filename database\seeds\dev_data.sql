-- Development seed data for TON Voucher Platform
-- This file contains sample data for development and testing

BEGIN;

-- Insert test admin user
-- Password: Admin123!
INSERT INTO users (id, email, telegram_id, password_hash, email_verified, role, created_at) 
VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    '<EMAIL>',
    'admin_tonsite',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G',
    TRUE,
    'admin',
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- Insert test regular users
-- Password for all: Test123!
INSERT INTO users (id, email, telegram_id, password_hash, email_verified, role, created_at) 
VALUES 
    (
        '550e8400-e29b-41d4-a716-446655440001',
        '<EMAIL>',
        'user1_test',
        '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        TRUE,
        'user',
        CURRENT_TIMESTAMP - INTERVAL '7 days'
    ),
    (
        '550e8400-e29b-41d4-a716-446655440002',
        '<EMAIL>',
        'user2_test',
        '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        TRUE,
        'user',
        CURRENT_TIMESTAMP - INTERVAL '5 days'
    ),
    (
        '550e8400-e29b-41d4-a716-446655440003',
        '<EMAIL>',
        'user3_test',
        '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        FALSE,
        'user',
        CURRENT_TIMESTAMP - INTERVAL '2 days'
    )
ON CONFLICT (email) DO NOTHING;

-- Insert test orders
INSERT INTO orders (id, user_id, status, amount, currency, memo, payment_address, created_at, updated_at)
VALUES 
    (
        '660e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440001',
        'completed',
        10.50,
        'TON',
        'Premium game pass purchase',
        'EQD1Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        CURRENT_TIMESTAMP - INTERVAL '6 days',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440001',
        'completed',
        25.00,
        'TON',
        'VIP access voucher',
        'EQD2Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        CURRENT_TIMESTAMP - INTERVAL '4 days',
        CURRENT_TIMESTAMP - INTERVAL '4 days'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440002',
        'completed',
        15.75,
        'TON',
        'Special edition content',
        'EQD3Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        CURRENT_TIMESTAMP - INTERVAL '3 days',
        CURRENT_TIMESTAMP - INTERVAL '3 days'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-446655440002',
        'payment_pending',
        20.00,
        'TON',
        'Deluxe package',
        'EQD4Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        CURRENT_TIMESTAMP - INTERVAL '1 hour',
        CURRENT_TIMESTAMP - INTERVAL '1 hour'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440005',
        '550e8400-e29b-41d4-a716-446655440003',
        'pending',
        5.99,
        'TON',
        'Starter pack',
        NULL,
        CURRENT_TIMESTAMP - INTERVAL '30 minutes',
        CURRENT_TIMESTAMP - INTERVAL '30 minutes'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test vouchers for completed orders
INSERT INTO vouchers (id, order_id, code, status, expires_at, created_at)
VALUES 
    (
        '770e8400-e29b-41d4-a716-446655440001',
        '660e8400-e29b-41d4-a716-446655440001',
        'GAME1234ABCD',
        'active',
        CURRENT_TIMESTAMP + INTERVAL '365 days',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    ),
    (
        '770e8400-e29b-41d4-a716-446655440002',
        '660e8400-e29b-41d4-a716-446655440002',
        'VIP5678EFGH',
        'redeemed',
        CURRENT_TIMESTAMP + INTERVAL '365 days',
        CURRENT_TIMESTAMP - INTERVAL '4 days'
    ),
    (
        '770e8400-e29b-41d4-a716-446655440003',
        '660e8400-e29b-41d4-a716-446655440003',
        'SPEC9012IJKL',
        'active',
        CURRENT_TIMESTAMP + INTERVAL '365 days',
        CURRENT_TIMESTAMP - INTERVAL '3 days'
    )
ON CONFLICT (id) DO NOTHING;

-- Update redeemed voucher
UPDATE vouchers 
SET redeemed_at = CURRENT_TIMESTAMP - INTERVAL '2 days',
    redeemed_by_user_id = '550e8400-e29b-41d4-a716-446655440001'
WHERE code = 'VIP5678EFGH';

-- Insert test transactions for completed orders
INSERT INTO transactions (id, order_id, hash, from_address, to_address, amount, fee, status, confirmations, created_at, updated_at)
VALUES 
    (
        '880e8400-e29b-41d4-a716-446655440001',
        '660e8400-e29b-41d4-a716-446655440001',
        'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
        'EQC1Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        'EQD1Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        10.50,
        0.01,
        'confirmed',
        15,
        CURRENT_TIMESTAMP - INTERVAL '6 days',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    ),
    (
        '880e8400-e29b-41d4-a716-446655440002',
        '660e8400-e29b-41d4-a716-446655440002',
        'b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1',
        'EQC2Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        'EQD2Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        25.00,
        0.015,
        'confirmed',
        20,
        CURRENT_TIMESTAMP - INTERVAL '4 days',
        CURRENT_TIMESTAMP - INTERVAL '4 days'
    ),
    (
        '880e8400-e29b-41d4-a716-446655440003',
        '660e8400-e29b-41d4-a716-446655440003',
        'c3d4e5f6789012345678901234567890123456789012345678901234567890a1b2',
        'EQC3Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        'EQD3Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        15.75,
        0.012,
        'confirmed',
        12,
        CURRENT_TIMESTAMP - INTERVAL '3 days',
        CURRENT_TIMESTAMP - INTERVAL '3 days'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test payment addresses
INSERT INTO payment_addresses (id, order_id, address, is_used, expires_at, created_at)
VALUES 
    (
        '990e8400-e29b-41d4-a716-446655440001',
        '660e8400-e29b-41d4-a716-446655440001',
        'EQD1Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        TRUE,
        CURRENT_TIMESTAMP - INTERVAL '5 days',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440002',
        '660e8400-e29b-41d4-a716-446655440002',
        'EQD2Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        TRUE,
        CURRENT_TIMESTAMP - INTERVAL '3 days',
        CURRENT_TIMESTAMP - INTERVAL '4 days'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440003',
        '660e8400-e29b-41d4-a716-446655440003',
        'EQD3Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        TRUE,
        CURRENT_TIMESTAMP - INTERVAL '2 days',
        CURRENT_TIMESTAMP - INTERVAL '3 days'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440004',
        '660e8400-e29b-41d4-a716-446655440004',
        'EQD4Lp1KcmGHFpE8eIvL1mnHT83b4HdB8HJxuSfq6Rq4zGyN',
        FALSE,
        CURRENT_TIMESTAMP + INTERVAL '29 minutes',
        CURRENT_TIMESTAMP - INTERVAL '1 hour'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test email logs
INSERT INTO email_logs (id, user_id, email_type, recipient_email, subject, status, created_at)
VALUES 
    (
        'aa0e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440001',
        'voucher-delivery',
        '<EMAIL>',
        'Your Premium Game Pass Voucher Code',
        'delivered',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    ),
    (
        'aa0e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440001',
        'voucher-delivery',
        '<EMAIL>',
        'Your VIP Access Voucher Code',
        'delivered',
        CURRENT_TIMESTAMP - INTERVAL '4 days'
    ),
    (
        'aa0e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440002',
        'voucher-delivery',
        '<EMAIL>',
        'Your Special Edition Content Voucher Code',
        'delivered',
        CURRENT_TIMESTAMP - INTERVAL '3 days'
    ),
    (
        'aa0e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-446655440003',
        'email-verification',
        '<EMAIL>',
        'Verify Your Email Address',
        'sent',
        CURRENT_TIMESTAMP - INTERVAL '2 days'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert test admin logs
INSERT INTO admin_logs (id, admin_user_id, action, resource_type, resource_id, details, created_at)
VALUES 
    (
        'bb0e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440000',
        'user_created',
        'user',
        '550e8400-e29b-41d4-a716-446655440001',
        '{"email": "<EMAIL>", "telegram_id": "user1_test"}',
        CURRENT_TIMESTAMP - INTERVAL '7 days'
    ),
    (
        'bb0e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440000',
        'voucher_generated',
        'voucher',
        '770e8400-e29b-41d4-a716-446655440001',
        '{"code": "GAME1234ABCD", "order_id": "660e8400-e29b-41d4-a716-446655440001"}',
        CURRENT_TIMESTAMP - INTERVAL '6 days'
    )
ON CONFLICT (id) DO NOTHING;

COMMIT;
