-- Migration: Add per-user rate limiting settings
-- Description: Add database settings for per-user rate limiting to complement existing IP-based rate limiting

-- Add per-user rate limiting settings
INSERT INTO system_settings (category, setting_key, setting_value, data_type, description, is_sensitive, is_readonly) VALUES
-- Per-user rate limiting settings (lower limits than IP-based to prevent abuse)
('rate_limiting', 'user_general_requests_per_window', '50', 'number', 'Per-user general API requests per 15-minute window', false, false),
('rate_limiting', 'user_admin_requests_per_window', '200', 'number', 'Per-user admin API requests per 15-minute window', false, false),
('rate_limiting', 'user_order_requests_per_window', '5', 'number', 'Per-user order creation requests per 15-minute window', false, false),
('rate_limiting', 'user_auth_requests_per_window', '3', 'number', 'Per-user authentication requests per 15-minute window', false, false)
ON CONFLICT (category, setting_key) DO UPDATE SET
  setting_value = EXCLUDED.setting_value,
  description = EXCLUDED.description,
  updated_at = CURRENT_TIMESTAMP;

-- Add validation rules for the new settings
UPDATE system_settings 
SET validation_rules = '{"min": 1, "max": 1000}'
WHERE category = 'rate_limiting' 
  AND setting_key IN (
    'user_general_requests_per_window',
    'user_admin_requests_per_window',
    'user_order_requests_per_window',
    'user_auth_requests_per_window'
  );
