import { Request, Response } from 'express';
import { executeQuery } from '../config/database';
import { logger } from '../config/logger';
import { sanitizeString } from '../utils/helpers';

// Get all products with filtering and search
export const getProducts = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const category = req.query.category as string;
    const search = req.query.search as string;
    const sortBy = req.query.sortBy as string || 'sort_order';
    const sortOrder = req.query.sortOrder as string || 'asc';
    const popular = req.query.popular as string;

    // Build query conditions
    let whereClause = 'WHERE p.available = true';
    const queryParams: any[] = [];

    if (category && category !== 'all') {
      whereClause += ` AND p.category = $${queryParams.length + 1}`;
      queryParams.push(category);
    }

    if (search) {
      whereClause += ` AND (p.name ILIKE $${queryParams.length + 1} OR p.description ILIKE $${queryParams.length + 1})`;
      queryParams.push(`%${search}%`);
    }

    if (popular === 'true') {
      whereClause += ' AND p.popular = true';
    }

    // Validate sort parameters
    const allowedSortFields = ['name', 'rating', 'min_amount', 'max_amount', 'sort_order', 'created_at'];
    const allowedSortOrders = ['asc', 'desc'];
    
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'sort_order';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toLowerCase() : 'asc';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) 
      FROM products p 
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get products
    const productsQuery = `
      SELECT p.id, p.name, p.description, p.category, p.min_amount, p.max_amount, 
             p.currency, p.rating, p.review_count, p.image_url, p.popular, 
             p.available, p.features, p.metadata, p.created_at,
             pc.name as category_name, pc.icon as category_icon
      FROM products p
      LEFT JOIN product_categories pc ON p.category = pc.id
      ${whereClause}
      ORDER BY p.${validSortBy} ${validSortOrder}, p.sort_order ASC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const productsResult = await executeQuery(productsQuery, queryParams);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        products: productsResult.rows.map((row: any) => ({
          id: row.id,
          name: row.name,
          description: row.description,
          category: row.category,
          categoryName: row.category_name,
          categoryIcon: row.category_icon,
          minAmount: parseFloat(row.min_amount),
          maxAmount: parseFloat(row.max_amount),
          currency: row.currency,
          rating: parseFloat(row.rating),
          reviewCount: row.review_count,
          image: row.image_url,
          popular: row.popular,
          available: row.available,
          features: row.features || [],
          metadata: row.metadata || {},
          createdAt: row.created_at,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get products',
    });
  }
};

// Get product by ID
export const getProductById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const productResult = await executeQuery(
      `SELECT p.id, p.name, p.description, p.category, p.min_amount, p.max_amount, 
              p.currency, p.rating, p.review_count, p.image_url, p.popular, 
              p.available, p.features, p.metadata, p.created_at,
              pc.name as category_name, pc.icon as category_icon
       FROM products p
       LEFT JOIN product_categories pc ON p.category = pc.id
       WHERE p.id = $1 AND p.available = true`,
      [id]
    );

    if (productResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
      });
    }

    const product = productResult.rows[0];

    res.json({
      success: true,
      data: {
        product: {
          id: product.id,
          name: product.name,
          description: product.description,
          category: product.category,
          categoryName: product.category_name,
          categoryIcon: product.category_icon,
          minAmount: parseFloat(product.min_amount),
          maxAmount: parseFloat(product.max_amount),
          currency: product.currency,
          rating: parseFloat(product.rating),
          reviewCount: product.review_count,
          image: product.image_url,
          popular: product.popular,
          available: product.available,
          features: product.features || [],
          metadata: product.metadata || {},
          createdAt: product.created_at,
        },
      },
    });
  } catch (error) {
    logger.error('Get product by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get product',
    });
  }
};

// Get all product categories
export const getCategories = async (req: Request, res: Response) => {
  try {
    const categoriesResult = await executeQuery(
      `SELECT pc.id, pc.name, pc.description, pc.icon, pc.sort_order,
              COUNT(p.id) as product_count
       FROM product_categories pc
       LEFT JOIN products p ON pc.id = p.category AND p.available = true
       WHERE pc.active = true
       GROUP BY pc.id, pc.name, pc.description, pc.icon, pc.sort_order
       ORDER BY pc.sort_order ASC, pc.name ASC`
    );

    res.json({
      success: true,
      data: {
        categories: categoriesResult.rows.map((row: any) => ({
          id: row.id,
          name: row.name,
          description: row.description,
          icon: row.icon,
          productCount: parseInt(row.product_count),
          sortOrder: row.sort_order,
        })),
      },
    });
  } catch (error) {
    logger.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get categories',
    });
  }
};

// Get featured/popular products
export const getFeaturedProducts = async (req: Request, res: Response) => {
  try {
    const limit = Math.min(parseInt(req.query.limit as string) || 6, 20);

    const productsResult = await executeQuery(
      `SELECT p.id, p.name, p.description, p.category, p.min_amount, p.max_amount, 
              p.currency, p.rating, p.review_count, p.image_url, p.popular, 
              p.available, p.features, p.metadata,
              pc.name as category_name, pc.icon as category_icon
       FROM products p
       LEFT JOIN product_categories pc ON p.category = pc.id
       WHERE p.available = true AND p.popular = true
       ORDER BY p.rating DESC, p.review_count DESC, p.sort_order ASC
       LIMIT $1`,
      [limit]
    );

    res.json({
      success: true,
      data: {
        products: productsResult.rows.map((row: any) => ({
          id: row.id,
          name: row.name,
          description: row.description,
          category: row.category,
          categoryName: row.category_name,
          categoryIcon: row.category_icon,
          minAmount: parseFloat(row.min_amount),
          maxAmount: parseFloat(row.max_amount),
          currency: row.currency,
          rating: parseFloat(row.rating),
          reviewCount: row.review_count,
          image: row.image_url,
          popular: row.popular,
          available: row.available,
          features: row.features || [],
          metadata: row.metadata || {},
        })),
      },
    });
  } catch (error) {
    logger.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get featured products',
    });
  }
};

// Search products
export const searchProducts = async (req: Request, res: Response) => {
  try {
    const { q: query } = req.query;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    if (!query || typeof query !== 'string' || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Search query must be at least 2 characters long',
      });
    }

    const sanitizedQuery = sanitizeString(query.trim());

    const searchResult = await executeQuery(
      `SELECT p.id, p.name, p.description, p.category, p.min_amount, p.max_amount, 
              p.currency, p.rating, p.review_count, p.image_url, p.popular,
              pc.name as category_name,
              ts_rank(to_tsvector('english', p.name || ' ' || p.description), plainto_tsquery('english', $1)) as rank
       FROM products p
       LEFT JOIN product_categories pc ON p.category = pc.id
       WHERE p.available = true 
       AND (
         to_tsvector('english', p.name || ' ' || p.description) @@ plainto_tsquery('english', $1)
         OR p.name ILIKE $2 
         OR p.description ILIKE $2
       )
       ORDER BY rank DESC, p.rating DESC, p.review_count DESC
       LIMIT $3`,
      [sanitizedQuery, `%${sanitizedQuery}%`, limit]
    );

    res.json({
      success: true,
      data: {
        products: searchResult.rows.map((row: any) => ({
          id: row.id,
          name: row.name,
          description: row.description,
          category: row.category,
          categoryName: row.category_name,
          minAmount: parseFloat(row.min_amount),
          maxAmount: parseFloat(row.max_amount),
          currency: row.currency,
          rating: parseFloat(row.rating),
          reviewCount: row.review_count,
          image: row.image_url,
          popular: row.popular,
        })),
        query: sanitizedQuery,
        total: searchResult.rows.length,
      },
    });
  } catch (error) {
    logger.error('Search products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search products',
    });
  }
};
