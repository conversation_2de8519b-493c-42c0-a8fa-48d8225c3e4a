-- Migration: Add user settings table
-- Description: Create user_settings table for storing user preferences

-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Notification settings
    notification_email BOOLEAN DEFAULT true,
    notification_push BO<PERSON>EAN DEFAULT true,
    notification_order_updates BOOLEAN DEFAULT true,
    notification_promotions BOOLEAN DEFAULT false,
    notification_security BOOLEAN DEFAULT true,
    
    -- Privacy settings
    privacy_profile_visibility VARCHAR(20) DEFAULT 'private' CHECK (privacy_profile_visibility IN ('public', 'private')),
    privacy_data_sharing BOOLEAN DEFAULT false,
    privacy_analytics BOOLEAN DEFAULT true,
    
    -- Appearance settings
    appearance_theme VARCHAR(20) DEFAULT 'system' CHECK (appearance_theme IN ('light', 'dark', 'system')),
    appearance_language VARCHAR(10) DEFAULT 'en',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(user_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_settings_created_at ON user_settings(created_at);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_user_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS trigger_user_settings_updated_at ON user_settings;
CREATE TRIGGER trigger_user_settings_updated_at
    BEFORE UPDATE ON user_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_user_settings_updated_at();

-- Insert default settings for existing users
INSERT INTO user_settings (user_id)
SELECT id FROM users 
WHERE id NOT IN (SELECT user_id FROM user_settings)
ON CONFLICT (user_id) DO NOTHING;
