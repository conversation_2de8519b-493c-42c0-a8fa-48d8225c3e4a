import { Router } from 'express';
import {
  getPublicSettings,
  getPublicSettingsByCategory,
} from '../controllers/publicSettingsController';
import { validateInput } from '../middleware/security';
import { param } from 'express-validator';

const router = Router();

// SECURE Category validation - Only allow safe categories
const categoryValidation = [
  param('category')
    .isIn(['payment', 'voucher', 'security', 'system'])
    .withMessage('Category must be one of: payment, voucher, security, system'),
];

/**
 * @route   GET /api/v1/public/settings
 * @desc    Get all public system settings
 * @access  Public
 */
router.get('/settings', getPublicSettings);

/**
 * @route   GET /api/v1/public/settings/:category
 * @desc    Get public settings by category
 * @access  Public
 */
router.get(
  '/settings/:category',
  validateInput(categoryValidation),
  getPublicSettingsByCategory
);

export default router;
