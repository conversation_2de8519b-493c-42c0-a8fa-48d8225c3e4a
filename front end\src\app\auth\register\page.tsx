'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Mail, User, Lock, MessageSquare } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useSecurity } from '@/hooks/useSecurity';
import DOMPurify from 'dompurify';

interface RegisterFormData {
  email: string;
  telegramId: string;
  password: string;
  confirmPassword: string;
  memo?: string;
  acceptTerms: boolean;
}

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();
  const { register: registerUser, loading } = useAuth();
  const {
    validatePassword,
    getPasswordRequirements,
    getPasswordMinLength,
    calculatePasswordStrength
  } = useSecurity();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormData>();

  const password = watch('password');

  const onSubmit = async (data: RegisterFormData) => {
    if (data.password !== data.confirmPassword) {
      return;
    }

    // Sanitize inputs
    const sanitizedData = {
      email: DOMPurify.sanitize(data.email.toLowerCase().trim()),
      telegramId: DOMPurify.sanitize(data.telegramId.trim()),
      password: data.password,
      memo: data.memo ? DOMPurify.sanitize(data.memo.trim()) : undefined,
    };

    const success = await registerUser(sanitizedData);
    if (success) {
      router.push('/auth/verify-email');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gradient">TON Vouchers</h1>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Create your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Join thousands of users purchasing premium vouchers with TON
          </p>
        </div>

        {/* Registration Form */}
        <div className="card">
          <div className="card-body">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="form-label">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Please enter a valid email address',
                      },
                    })}
                    type="email"
                    className={`form-input pl-10 ${errors.email ? 'form-input-error' : ''}`}
                    placeholder="Enter your email"
                    autoComplete="email"
                  />
                </div>
                {errors.email && (
                  <p className="form-error">{errors.email.message}</p>
                )}
              </div>

              {/* Telegram ID Field */}
              <div>
                <label htmlFor="telegramId" className="form-label">
                  Telegram ID
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('telegramId', {
                      required: 'Telegram ID is required',
                      pattern: {
                        value: /^[a-zA-Z0-9_]{5,32}$/,
                        message: 'Telegram ID must be 5-32 characters, alphanumeric and underscores only',
                      },
                    })}
                    type="text"
                    className={`form-input pl-10 ${errors.telegramId ? 'form-input-error' : ''}`}
                    placeholder="Enter your Telegram ID"
                    autoComplete="username"
                  />
                </div>
                {errors.telegramId && (
                  <p className="form-error">{errors.telegramId.message}</p>
                )}
                <p className="form-help">
                  Your Telegram username without the @ symbol
                </p>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="form-label">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('password', {
                      required: 'Password is required',
                      validate: (value) => {
                        const validation = validatePassword(value);
                        return validation.isValid || validation.errors[0];
                      },
                    })}
                    type={showPassword ? 'text' : 'password'}
                    className={`form-input pl-10 pr-10 ${errors.password ? 'form-input-error' : ''}`}
                    placeholder="Enter your password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="form-error">{errors.password.message}</p>
                )}
                {/* Dynamic Password Requirements */}
                <div className="mt-2">
                  <p className="text-xs text-gray-600 mb-1">Password requirements:</p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    {getPasswordRequirements().map((requirement, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                        {requirement}
                      </li>
                    ))}
                  </ul>

                  {/* Password Strength Indicator */}
                  {password && (
                    <div className="mt-2">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-600">Password strength:</span>
                        <span className="text-xs text-gray-600">{calculatePasswordStrength(password)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            calculatePasswordStrength(password) < 50
                              ? 'bg-red-500'
                              : calculatePasswordStrength(password) < 80
                              ? 'bg-yellow-500'
                              : 'bg-green-500'
                          }`}
                          style={{ width: `${calculatePasswordStrength(password)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Confirm Password Field */}
              <div>
                <label htmlFor="confirmPassword" className="form-label">
                  Confirm Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('confirmPassword', {
                      required: 'Please confirm your password',
                      validate: (value) =>
                        value === password || 'Passwords do not match',
                    })}
                    type={showConfirmPassword ? 'text' : 'password'}
                    className={`form-input pl-10 pr-10 ${errors.confirmPassword ? 'form-input-error' : ''}`}
                    placeholder="Confirm your password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="form-error">{errors.confirmPassword.message}</p>
                )}
              </div>

              {/* Memo Field */}
              <div>
                <label htmlFor="memo" className="form-label">
                  Memo (Optional)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MessageSquare className="h-5 w-5 text-gray-400" />
                  </div>
                  <textarea
                    {...register('memo', {
                      maxLength: {
                        value: 500,
                        message: 'Memo must be less than 500 characters',
                      },
                    })}
                    className={`form-input pl-10 ${errors.memo ? 'form-input-error' : ''}`}
                    placeholder="Optional memo or notes"
                    rows={3}
                  />
                </div>
                {errors.memo && (
                  <p className="form-error">{errors.memo.message}</p>
                )}
              </div>

              {/* Terms and Conditions */}
              <div className="flex items-center">
                <input
                  {...register('acceptTerms', {
                    required: 'You must accept the terms and conditions',
                  })}
                  type="checkbox"
                  className="h-4 w-4 text-ton-600 focus:ring-ton-500 border-gray-300 rounded"
                />
                <label htmlFor="acceptTerms" className="ml-2 block text-sm text-gray-900">
                  I agree to the{' '}
                  <Link href="/terms" className="text-ton-600 hover:text-ton-500">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-ton-600 hover:text-ton-500">
                    Privacy Policy
                  </Link>
                </label>
              </div>
              {errors.acceptTerms && (
                <p className="form-error">{errors.acceptTerms.message}</p>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full"
              >
                {loading ? (
                  <>
                    <div className="spinner-sm mr-2" />
                    Creating Account...
                  </>
                ) : (
                  'Create Account'
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Login Link */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              href="/auth/login"
              className="font-medium text-ton-600 hover:text-ton-500"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
