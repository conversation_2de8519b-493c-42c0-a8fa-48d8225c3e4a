// Validation utilities shared between frontend and backend

export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const TELEGRAM_ID_REGEX = /^[a-zA-Z0-9_]{5,32}$/;
export const VOUCHER_CODE_REGEX = /^[A-Z0-9]{12}$/;

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email is required');
  } else if (!EMAIL_REGEX.test(email)) {
    errors.push('Invalid email format');
  } else if (email.length > 255) {
    errors.push('Email must be less than 255 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateTelegramId = (telegramId: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!telegramId) {
    errors.push('Telegram ID is required');
  } else if (!TELEGRAM_ID_REGEX.test(telegramId)) {
    errors.push('Telegram ID must be 5-32 characters, alphanumeric and underscores only');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }
    if (!/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/(?=.*[@$!%*?&])/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateMemo = (memo: string): ValidationResult => {
  const errors: string[] = [];
  
  if (memo && memo.length > 500) {
    errors.push('Memo must be less than 500 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateAmount = (amount: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!amount) {
    errors.push('Amount is required');
  } else {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) {
      errors.push('Amount must be a valid number');
    } else if (numAmount <= 0) {
      errors.push('Amount must be greater than 0');
    } else if (numAmount > 1000000) {
      errors.push('Amount must be less than 1,000,000');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateVoucherCode = (code: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!code) {
    errors.push('Voucher code is required');
  } else if (!VOUCHER_CODE_REGEX.test(code)) {
    errors.push('Invalid voucher code format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateTONAddress = (address: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!address) {
    errors.push('TON address is required');
  } else if (address.length !== 48) {
    errors.push('TON address must be 48 characters long');
  } else if (!/^[A-Za-z0-9+/=_-]+$/.test(address)) {
    errors.push('Invalid TON address format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Sanitization utilities
export const sanitizeString = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

export const sanitizeTelegramId = (telegramId: string): string => {
  return telegramId.trim().replace(/[^a-zA-Z0-9_]/g, '');
};
