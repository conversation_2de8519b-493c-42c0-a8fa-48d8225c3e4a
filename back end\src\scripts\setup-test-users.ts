import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

// Direct database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

const setupTestUsers = async () => {
  const db = new Pool(dbConfig);
  
  try {
    console.log('👤 Setting up test users...');

    // Test database connection
    await db.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Test users to create
    const testUsers = [
      {
        email: '<EMAIL>',
        telegramId: 'test_user_123',
        password: 'TestPassword123!',
        role: 'admin',
        name: 'Admin'
      },
      {
        email: '<EMAIL>',
        telegramId: 'demo_user_456',
        password: 'DemoPassword123!',
        role: 'user',
        name: 'Demo User'
      },
      {
        email: '<EMAIL>',
        telegramId: 'customer_789',
        password: 'CustomerPass123!',
        role: 'user',
        name: 'Customer Test'
      }
    ];

    console.log(`📝 Creating ${testUsers.length} test users...`);

    for (const user of testUsers) {
      // Check if user already exists
      const existingUser = await db.query(
        'SELECT id, email FROM users WHERE email = $1',
        [user.email]
      );

      if (existingUser.rows.length === 0) {
        // Hash password
        const hashedPassword = await bcrypt.hash(user.password, 12);
        
        // Create user
        const userResult = await db.query(
          `INSERT INTO users (id, email, telegram_id, password_hash, email_verified, role, created_at, updated_at)
           VALUES ($1, $2, $3, $4, true, $5, NOW(), NOW())
           RETURNING id, email`,
          [uuidv4(), user.email, user.telegramId, hashedPassword, user.role]
        );
        
        const createdUser = userResult.rows[0];
        console.log(`✅ Created user: ${createdUser.email} (ID: ${createdUser.id})`);
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   🔑 Password: ${user.password}`);
        console.log(`   👤 Telegram ID: ${user.telegramId}`);
        console.log(`   🎭 Role: ${user.role}`);
        console.log('');
      } else {
        const existingUserData = existingUser.rows[0];
        console.log(`⚠️  User already exists: ${existingUserData.email} (ID: ${existingUserData.id})`);
        console.log(`   🔑 Password: ${user.password} (if unchanged)`);
        console.log('');
      }
    }

    // Display summary
    console.log('📊 Test Users Summary:');
    console.log('='.repeat(50));
    
    for (const user of testUsers) {
      console.log(`📧 ${user.email}`);
      console.log(`🔑 ${user.password}`);
      console.log(`👤 ${user.telegramId}`);
      console.log(`🎭 ${user.role}`);
      console.log('-'.repeat(30));
    }

    console.log('🎉 Test users setup completed successfully!');
    console.log('');
    console.log('💡 You can now use these credentials to:');
    console.log('   1. Login to the frontend at http://localhost:3000');
    console.log('   2. Test the order creation functionality');
    console.log('   3. Test the product browsing and purchasing flow');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   1. Start the backend: npm run dev');
    console.log('   2. Start the frontend: npm run dev');
    console.log('   3. Login with any of the test user credentials');
    console.log('   4. Navigate to /dashboard/orders/new to test order creation');
    
  } catch (error) {
    console.error('❌ Error setting up test users:', error);
    throw error;
  } finally {
    await db.end();
  }
};

// Run the setup
setupTestUsers()
  .then(() => {
    console.log('✅ Test users setup finished');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test users setup failed:', error);
    process.exit(1);
  });
