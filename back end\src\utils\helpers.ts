import { randomBytes, createHash } from 'crypto';

/**
 * Generate a secure random token
 */
export const generateSecureToken = (length: number = 32): string => {
  return randomBytes(length).toString('hex');
};

/**
 * Generate a secure random voucher code
 */
export const generateVoucherCode = (length: number = 12): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  const bytes = randomBytes(length);

  for (let i = 0; i < length; i++) {
    result += chars[bytes[i] % chars.length];
  }

  return result;
};

/**
 * Hash a string using SHA-256
 */
export const hashString = (input: string): string => {
  return createHash('sha256').update(input).digest('hex');
};

/**
 * Format currency amount for display
 */
export const formatCurrency = (amount: string | number, currency: string = 'TON'): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `${numAmount.toFixed(2)} ${currency}`;
};

/**
 * Format date for display
 */
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Sleep utility for async operations
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry utility for async operations
 */
export const retry = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      await sleep(delay * attempt);
    }
  }
  
  throw lastError!;
};

/**
 * Check if a value is empty
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Mask sensitive data for logging
 */
export const maskSensitiveData = (data: any): any => {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'hash'];
  const masked = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in masked) {
      masked[field] = '***MASKED***';
    }
  }
  
  return masked;
};

/**
 * Generate a unique order ID
 */
export const generateOrderId = (): string => {
  const timestamp = Date.now().toString(36);
  const random = randomBytes(4).toString('hex');
  return `ORD-${timestamp}-${random}`.toUpperCase();
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate Telegram ID format
 */
export const isValidTelegramId = (telegramId: string): boolean => {
  const telegramRegex = /^[a-zA-Z0-9_]{5,32}$/;
  return telegramRegex.test(telegramId);
};

/**
 * Sanitize string for safe output
 */
export const sanitizeString = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

/**
 * Generate pagination metadata
 */
export const generatePaginationMeta = (
  total: number,
  page: number,
  limit: number
) => {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext,
    hasPrev,
  };
};

/**
 * Convert TON amount from nanotons to TON
 */
export const fromNanoTON = (nanotons: string | number): string => {
  const nano = typeof nanotons === 'string' ? BigInt(nanotons) : BigInt(nanotons);
  const ton = Number(nano) / 1000000000; // 1 TON = 10^9 nanotons
  return ton.toFixed(9);
};

/**
 * Convert TON amount to nanotons
 */
export const toNanoTON = (ton: string | number): string => {
  const tonAmount = typeof ton === 'string' ? parseFloat(ton) : ton;
  const nanotons = Math.floor(tonAmount * 1000000000);
  return nanotons.toString();
};

/**
 * Validate TON transaction hash format
 */
export const isValidTONTransactionHash = (hash: string): boolean => {
  return /^[a-fA-F0-9]{64}$/.test(hash);
};

/**
 * Generate a secure session ID
 */
export const generateSessionId = (): string => {
  return generateSecureToken(32);
};
