import { Router } from 'express';
import {
  getVoucherByCode,
  getUserVouchers,
  redeemVoucher,
  checkVoucherStatus,
  getVoucherStats,
  downloadVoucherDetails,
} from '../controllers/voucherController';
import {
  validateInput,
} from '../middleware/security';
import { authenticate, requireUser } from '../middleware/auth';
import { body, query, param } from 'express-validator';

const router = Router();

// Voucher code validation
const voucherCodeValidation = [
  param('code')
    .matches(/^[A-Z0-9]{12}$/)
    .withMessage('Invalid voucher code format'),
];

// Redeem voucher validation
const redeemVoucherValidation = [
  body('code')
    .matches(/^[A-Z0-9]{12}$/)
    .withMessage('Invalid voucher code format'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Search validation
const searchValidation = [
  query('search')
    .optional()
    .isLength({ max: 255 })
    .trim()
    .escape()
    .withMessage('Search term must be less than 255 characters'),
];

// Voucher status validation
const voucherStatusValidation = [
  query('status')
    .optional()
    .isIn(['active', 'redeemed', 'expired', 'cancelled'])
    .withMessage('Invalid voucher status'),
];

/**
 * @route   GET /api/v1/vouchers
 * @desc    Get user vouchers with pagination, filtering, and search
 * @access  Private (User)
 */
router.get(
  '/',
  authenticate,
  requireUser,
  validateInput([...paginationValidation, ...voucherStatusValidation, ...searchValidation]),
  getUserVouchers
);

/**
 * @route   GET /api/v1/vouchers/stats
 * @desc    Get user voucher statistics
 * @access  Private (User)
 */
router.get(
  '/stats',
  authenticate,
  requireUser,
  getVoucherStats
);

/**
 * @route   GET /api/v1/vouchers/:code
 * @desc    Get voucher by code (user's own vouchers only)
 * @access  Private (User)
 */
router.get(
  '/:code',
  authenticate,
  requireUser,
  validateInput(voucherCodeValidation),
  getVoucherByCode
);

/**
 * @route   GET /api/v1/vouchers/:code/download
 * @desc    Download voucher details as text file
 * @access  Private (User)
 */
router.get(
  '/:code/download',
  authenticate,
  requireUser,
  validateInput(voucherCodeValidation),
  downloadVoucherDetails
);

/**
 * @route   GET /api/v1/vouchers/:code/status
 * @desc    Check voucher status (public endpoint for verification)
 * @access  Public
 */
router.get(
  '/:code/status',
  validateInput(voucherCodeValidation),
  checkVoucherStatus
);

/**
 * @route   POST /api/v1/vouchers/redeem
 * @desc    Redeem a voucher code
 * @access  Private (User)
 */
router.post(
  '/redeem',
  authenticate,
  requireUser,
  validateInput(redeemVoucherValidation),
  redeemVoucher
);

export default router;
