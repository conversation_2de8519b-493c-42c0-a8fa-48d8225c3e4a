import { Pool } from 'pg';
import axios from 'axios';
//import { logger } from '../utils/logger';

// Database configuration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'tonsite_dev',
  password: '1234',
  port: 5432,
});

const API_BASE_URL = 'http://localhost:3001/api/v1';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message: string;
  details?: any;
}

const results: TestResult[] = [];

const addResult = (test: string, status: 'PASS' | 'FAIL', message: string, details?: any) => {
  results.push({ test, status, message, details });
  console.log(`${status === 'PASS' ? '✅' : '❌'} ${test}: ${message}`);
  if (details) {
    console.log('   Details:', details);
  }
};

async function testResendVerificationEndpoint() {
  console.log('\n🧪 Testing Resend Verification Functionality\n');

  try {
    // Test 1: Create a test user for verification testing
    console.log('Test 1: Setting up test user...');
    
    const testEmail = '<EMAIL>';
    const testTelegramId = 'test_resend_user';
    
    // Clean up any existing test user
    await pool.query('DELETE FROM users WHERE email = $1', [testEmail]);
    
    // Create test user with unverified email
    const hashedPassword = '$2b$12$test.hash.for.testing.purposes.only';
    const emailVerificationToken = 'test-token-12345';
    const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    
    const insertResult = await pool.query(`
      INSERT INTO users (
        email, telegram_id, password_hash, email_verified, 
        email_verification_token, email_verification_expires
      ) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id
    `, [testEmail, testTelegramId, hashedPassword, false, emailVerificationToken, emailVerificationExpires]);
    
    const userId = insertResult.rows[0].id;
    addResult('Test 1', 'PASS', 'Test user created successfully', { userId, email: testEmail });

    // Test 2: Test resend verification with valid email
    console.log('\nTest 2: Testing resend verification with valid email...');
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/resend-verification`, {
        email: testEmail
      });
      
      if (response.data.success) {
        addResult('Test 2', 'PASS', 'Resend verification successful', response.data);
        
        // Verify that the token was updated in database
        const userCheck = await pool.query(
          'SELECT email_verification_token, email_verification_expires FROM users WHERE id = $1',
          [userId]
        );
        
        const user = userCheck.rows[0];
        if (user.email_verification_token !== emailVerificationToken) {
          addResult('Test 2.1', 'PASS', 'Verification token was updated in database');
        } else {
          addResult('Test 2.1', 'FAIL', 'Verification token was not updated in database');
        }
        
        if (new Date(user.email_verification_expires) > new Date()) {
          addResult('Test 2.2', 'PASS', 'Verification expiry was updated to future date');
        } else {
          addResult('Test 2.2', 'FAIL', 'Verification expiry is not in the future');
        }
      } else {
        addResult('Test 2', 'FAIL', 'Resend verification failed', response.data);
      }
    } catch (error: any) {
      addResult('Test 2', 'FAIL', 'Resend verification request failed', {
        status: error.response?.status,
        data: error.response?.data
      });
    }

    // Test 3: Test resend verification with non-existent email
    console.log('\nTest 3: Testing resend verification with non-existent email...');
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/resend-verification`, {
        email: '<EMAIL>'
      });
      
      addResult('Test 3', 'FAIL', 'Should have failed for non-existent email', response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        addResult('Test 3', 'PASS', 'Correctly returned 404 for non-existent email');
      } else {
        addResult('Test 3', 'FAIL', 'Unexpected error for non-existent email', {
          status: error.response?.status,
          data: error.response?.data
        });
      }
    }

    // Test 4: Test resend verification for already verified user
    console.log('\nTest 4: Testing resend verification for already verified user...');
    
    // Mark user as verified
    await pool.query(
      'UPDATE users SET email_verified = true, email_verification_token = NULL WHERE id = $1',
      [userId]
    );
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/resend-verification`, {
        email: testEmail
      });
      
      addResult('Test 4', 'FAIL', 'Should have failed for already verified email', response.data);
    } catch (error: any) {
      if (error.response?.status === 400 && error.response?.data?.error?.includes('already verified')) {
        addResult('Test 4', 'PASS', 'Correctly rejected already verified email');
      } else {
        addResult('Test 4', 'FAIL', 'Unexpected error for already verified email', {
          status: error.response?.status,
          data: error.response?.data
        });
      }
    }

    // Test 5: Test resend verification with invalid email format
    console.log('\nTest 5: Testing resend verification with invalid email format...');
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/resend-verification`, {
        email: 'invalid-email-format'
      });
      
      addResult('Test 5', 'FAIL', 'Should have failed for invalid email format', response.data);
    } catch (error: any) {
      if (error.response?.status === 400) {
        addResult('Test 5', 'PASS', 'Correctly rejected invalid email format');
      } else {
        addResult('Test 5', 'FAIL', 'Unexpected error for invalid email format', {
          status: error.response?.status,
          data: error.response?.data
        });
      }
    }

    // Test 6: Test resend verification with missing email
    console.log('\nTest 6: Testing resend verification with missing email...');
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/resend-verification`, {});
      
      addResult('Test 6', 'FAIL', 'Should have failed for missing email', response.data);
    } catch (error: any) {
      if (error.response?.status === 400) {
        addResult('Test 6', 'PASS', 'Correctly rejected missing email');
      } else {
        addResult('Test 6', 'FAIL', 'Unexpected error for missing email', {
          status: error.response?.status,
          data: error.response?.data
        });
      }
    }

    // Cleanup
    console.log('\nCleaning up test data...');
    await pool.query('DELETE FROM users WHERE email = $1', [testEmail]);
    addResult('Cleanup', 'PASS', 'Test data cleaned up successfully');

  } catch (error) {
    addResult('Setup', 'FAIL', 'Test setup failed', error);
  }
}

async function main() {
  try {
    await testResendVerificationEndpoint();
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = results.filter(r => r.status === 'PASS').length;
    const failed = results.filter(r => r.status === 'FAIL').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`   - ${result.test}: ${result.message}`);
      });
    }
    
    process.exit(failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('Test execution failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}
