'use client';

import { useState, useEffect } from 'react';
import { QrC<PERSON>, Copy, ExternalLink, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useTonConnect } from '@/contexts/TonConnectContext';
import { ordersApi, paymentsApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

interface TonPaymentProps {
  orderId: string;
  amount: string;
  currency: string;
  paymentAddress: string;
  expiresAt: string;
  onPaymentComplete?: (transactionHash: string) => void;
  onPaymentFailed?: (error: string) => void;
}

interface PaymentStatus {
  status: 'pending' | 'payment_pending' | 'paid' | 'completed' | 'failed' | 'expired';
  transactionHash?: string;
}

export default function TonPayment({
  orderId,
  amount,
  currency,
  paymentAddress,
  expiresAt,
  onPaymentComplete,
  onPaymentFailed,
}: TonPaymentProps) {
  const { wallet, connected, sendTransaction } = useTonConnect();
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>({ status: 'pending' });
  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [showQR, setShowQR] = useState(false);

  // Update countdown timer
  useEffect(() => {
    const updateTimer = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const difference = expiry - now;

      if (difference > 0) {
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);
        setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
      } else {
        setTimeLeft('Expired');
        setPaymentStatus({ status: 'expired' });
      }
    };

    updateTimer();
    const timer = setInterval(updateTimer, 1000);

    return () => clearInterval(timer);
  }, [expiresAt]);

  // Poll payment status
  useEffect(() => {
    const pollStatus = async () => {
      try {
        const response = await paymentsApi.getStatus(orderId);
        if (response.success && response.data?.order) {
          const newStatus = response.data.order.status;
          setPaymentStatus({ 
            status: newStatus,
            transactionHash: response.data.order.transactionHash 
          });

          if (newStatus === 'completed' && onPaymentComplete && response.data.order.transactionHash) {
            onPaymentComplete(response.data.order.transactionHash);
          } else if (newStatus === 'failed' && onPaymentFailed) {
            onPaymentFailed('Payment failed');
          }
        }
      } catch (error) {
        console.error('Failed to poll payment status:', error);
      }
    };

    // Poll every 5 seconds if payment is pending
    if (['pending', 'payment_pending'].includes(paymentStatus.status)) {
      const interval = setInterval(pollStatus, 5000);
      return () => clearInterval(interval);
    }
  }, [orderId, paymentStatus.status, onPaymentComplete, onPaymentFailed]);

  const handleWalletPayment = async () => {
    if (!connected || !wallet) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      setLoading(true);

      // Create transaction
      const transaction = {
        validUntil: Math.floor(Date.now() / 1000) + 300, // 5 minutes
        messages: [
          {
            address: paymentAddress,
            amount: (parseFloat(amount) * 1000000000).toString(), // Convert to nanoTON
            payload: btoa(`Order:${orderId}`), // Base64 encode memo
          },
        ],
      };

      // Send transaction through wallet
      const result = await sendTransaction(transaction);
      
      // Update payment status
      setPaymentStatus({ status: 'payment_pending' });
      
      toast.success('Transaction sent! Waiting for confirmation...');
    } catch (error) {
      console.error('Payment failed:', error);
      toast.error('Payment failed. Please try again.');
      if (onPaymentFailed) {
        onPaymentFailed(error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const copyAddress = () => {
    navigator.clipboard.writeText(paymentAddress);
    toast.success('Payment address copied to clipboard');
  };

  const copyAmount = () => {
    navigator.clipboard.writeText(amount);
    toast.success('Amount copied to clipboard');
  };

  const openInExplorer = () => {
    const explorerUrl = `https://testnet.tonviewer.com/${paymentAddress}`;
    window.open(explorerUrl, '_blank');
  };

  const getStatusIcon = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success-500" />;
      case 'failed':
      case 'expired':
        return <AlertCircle className="h-5 w-5 text-error-500" />;
      case 'payment_pending':
        return <Clock className="h-5 w-5 text-warning-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return 'Payment completed successfully!';
      case 'failed':
        return 'Payment failed. Please try again.';
      case 'expired':
        return 'Payment window has expired.';
      case 'payment_pending':
        return 'Payment sent. Waiting for confirmation...';
      default:
        return 'Waiting for payment...';
    }
  };

  const getStatusColor = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return 'text-success-600 bg-success-50 border-success-200';
      case 'failed':
      case 'expired':
        return 'text-error-600 bg-error-50 border-error-200';
      case 'payment_pending':
        return 'text-warning-600 bg-warning-50 border-warning-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">TON Payment</h3>
          <p className="text-sm text-gray-500">Order #{orderId.slice(0, 8)}</p>
        </div>

        <div className="card-body space-y-6">
          {/* Status */}
          <div className={`flex items-center p-3 rounded-lg border ${getStatusColor()}`}>
            {getStatusIcon()}
            <span className="ml-2 text-sm font-medium">{getStatusText()}</span>
          </div>

          {/* Timer */}
          {!['completed', 'failed', 'expired'].includes(paymentStatus.status) && (
            <div className="text-center">
              <p className="text-sm text-gray-600">Time remaining:</p>
              <p className={`text-2xl font-bold ${timeLeft === 'Expired' ? 'text-error-600' : 'text-gray-900'}`}>
                {timeLeft}
              </p>
            </div>
          )}

          {/* Payment Details */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount to Pay
              </label>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-lg font-bold text-gray-900">
                  {amount} {currency}
                </span>
                <button
                  onClick={copyAmount}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded"
                  title="Copy amount"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Address
              </label>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-mono text-gray-900 truncate mr-2">
                  {paymentAddress}
                </span>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={copyAddress}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Copy address"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    onClick={openInExplorer}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="View in explorer"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Actions */}
          {!['completed', 'failed', 'expired'].includes(paymentStatus.status) && (
            <div className="space-y-3">
              {connected ? (
                <button
                  onClick={handleWalletPayment}
                  disabled={loading || paymentStatus.status === 'payment_pending'}
                  className="btn-primary w-full"
                >
                  {loading ? (
                    <>
                      <div className="spinner-sm mr-2" />
                      Sending Payment...
                    </>
                  ) : paymentStatus.status === 'payment_pending' ? (
                    'Payment Sent - Waiting for Confirmation'
                  ) : (
                    `Pay ${amount} TON`
                  )}
                </button>
              ) : (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    Connect your TON wallet to pay instantly
                  </p>
                  <button className="btn-outline w-full">
                    Connect Wallet to Pay
                  </button>
                </div>
              )}

              <div className="text-center">
                <button
                  onClick={() => setShowQR(!showQR)}
                  className="btn-ghost inline-flex items-center"
                >
                  <QrCode className="h-4 w-4 mr-2" />
                  {showQR ? 'Hide' : 'Show'} QR Code
                </button>
              </div>
            </div>
          )}

          {/* Transaction Hash */}
          {paymentStatus.transactionHash && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Hash
              </label>
              <div className="p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-mono text-gray-900 break-all">
                  {paymentStatus.transactionHash}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
