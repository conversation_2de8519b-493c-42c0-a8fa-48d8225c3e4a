import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';

// Hardcoded credentials for local dev testing
const DB_CONFIG = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

// Test database connection
let testDb: Pool;

// Initialize test database connection
const initTestDb = async () => {
  testDb = new Pool(DB_CONFIG);
  await testDb.query('SELECT NOW()');
  console.log('✅ Test database connection established');
};

// Files to scan for hardcoded values (relative to back/src/scripts directory)
const FILES_TO_SCAN = [
  '../middleware/security.ts',
  '../middleware/auth.ts',
  '../controllers/authController.ts',
  '../controllers/voucherController.ts',
  '../controllers/adminController.ts',
  '../app.ts',
  '../routes/admin.ts',
  '../middleware/dynamicValidation.ts',
];

// Patterns that indicate hardcoded values that should be replaced (excluding fallback values)
const HARDCODED_PATTERNS = [
  // Rate limiting values (only flag if NOT marked as fallback)
  { pattern: /windowMs:\s*\d+\s*\*\s*\d+\s*\*\s*\d+(?!.*(?:fallback|will be updated))/i, description: 'Rate limit window hardcoded' },
  { pattern: /max:\s*\d+(?!.*(?:getSetting|fallback|Fallback))/i, description: 'Rate limit max requests hardcoded' },

  // JWT expiration times (only flag if NOT marked as fallback)
  { pattern: /expiresIn:\s*['"`]\d+[mhd]['"`](?!.*(?:fallback|Fallback))/i, description: 'JWT expiration time hardcoded' },

  // Bcrypt rounds
  { pattern: /bcrypt\.hash.*,\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'Bcrypt rounds hardcoded' },

  // Business rule values (only flag if NOT marked as fallback)
  { pattern: /min:\s*0\.01(?!.*(?:getSetting|fallback|Fallback))/i, description: 'Min order amount hardcoded' },
  { pattern: /max:\s*1000000(?!.*(?:getSetting|fallback|Fallback))/i, description: 'Max order amount hardcoded' },

  // Pagination limits (only flag if NOT marked as fallback)
  { pattern: /limit.*:\s*\d+(?!.*(?:getSetting|fallback|Fallback))/i, description: 'Pagination limit hardcoded' },

  // File upload sizes (only flag if NOT marked as fallback)
  { pattern: /maxFileSize:\s*\d+\s*\*\s*\d+\s*\*\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'File upload size hardcoded' },

  // Security values
  { pattern: /maxLoginAttempts:\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'Max login attempts hardcoded' },
  { pattern: /lockoutDuration:\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'Lockout duration hardcoded' },

  // Voucher settings
  { pattern: /voucherCodeLength:\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'Voucher code length hardcoded' },
  { pattern: /expiryDays:\s*\d+(?!.*(?:fallback|Fallback))/i, description: 'Voucher expiry days hardcoded' },
];

// Expected database settings that should exist
const EXPECTED_SETTINGS = [
  { category: 'rate_limiting', key: 'general_window_ms' },
  { category: 'rate_limiting', key: 'general_max_requests' },
  { category: 'rate_limiting', key: 'auth_window_ms' },
  { category: 'rate_limiting', key: 'auth_max_requests' },
  { category: 'rate_limiting', key: 'admin_window_ms' },
  { category: 'rate_limiting', key: 'admin_max_requests' },
  { category: 'security', key: 'jwt_access_expires_in' },
  { category: 'security', key: 'jwt_refresh_expires_in' },
  { category: 'security', key: 'bcrypt_rounds' },
  { category: 'security', key: 'max_login_attempts' },
  { category: 'security', key: 'lockout_duration_minutes' },
  { category: 'security', key: 'password_min_length' },
  { category: 'business_rules', key: 'voucher_code_length' },
  { category: 'business_rules', key: 'voucher_expiry_days' },
  { category: 'business_rules', key: 'min_order_amount' },
  { category: 'business_rules', key: 'max_order_amount' },
  { category: 'business_rules', key: 'email_verification_expires_hours' },
  { category: 'business_rules', key: 'password_reset_expires_hours' },
  { category: 'file_upload', key: 'max_file_size' },
  { category: 'file_upload', key: 'upload_path' },
  { category: 'file_upload', key: 'allowed_mime_types' },
  { category: 'pagination', key: 'default_page' },
  { category: 'pagination', key: 'default_limit' },
  { category: 'pagination', key: 'max_limit' },
  { category: 'cache', key: 'user_session_ttl' },
  { category: 'cache', key: 'settings_cache_ttl' },
  { category: 'middleware', key: 'performance_monitoring_threshold' },
  { category: 'middleware', key: 'request_size_monitoring_limit' },
  { category: 'middleware', key: 'request_size_limit' },
  { category: 'middleware', key: 'memo_max_length' },
];

const scanFileForHardcodedValues = (filePath: string): Array<{ line: number; content: string; pattern: string }> => {
  const findings: Array<{ line: number; content: string; pattern: string }> = [];
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`    ⚠️  File not found: ${filePath}`);
      return findings;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      HARDCODED_PATTERNS.forEach(({ pattern, description }) => {
        if (pattern.test(line)) {
          // Skip lines that are clearly fallback values or using dynamic settings
          const lineContent = line.toLowerCase();
          const shouldSkip =
            line.includes('getSetting') ||
            line.includes('settingsService') ||
            line.includes('await') ||
            lineContent.includes('fallback') ||
            lineContent.includes('will be updated') ||
            lineContent.includes('default') ||
            lineContent.includes('backward compatibility') ||
            (lineContent.includes('catch') && lineContent.includes('error')) ||
            lineContent.includes('// fallback') ||
            lineContent.includes('(fallback') ||
            lineContent.includes('fallback value') ||
            lineContent.includes('fallback validation');

          if (!shouldSkip) {
            findings.push({
              line: index + 1,
              content: line.trim(),
              pattern: description,
            });
          }
        }
      });
    });
  } catch (error) {
    console.error(`    ❌ Error scanning file ${filePath}:`, error);
  }

  return findings;
};

const checkDatabaseSettings = async (): Promise<{ missing: any[]; existing: any[] }> => {
  const missing: any[] = [];
  const existing: any[] = [];

  for (const setting of EXPECTED_SETTINGS) {
    try {
      const result = await testDb.query(
        'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
        [setting.category, setting.key]
      );

      if (result.rows.length === 0) {
        missing.push(setting);
      } else {
        existing.push({
          ...setting,
          value: result.rows[0].setting_value,
          dataType: result.rows[0].data_type,
        });
      }
    } catch (error) {
      console.error(`    ❌ Error checking setting ${setting.category}:${setting.key}:`, error);
      missing.push(setting);
    }
  }

  return { missing, existing };
};

const testHardcodedValuesReplacement = async () => {
  console.log('🧪 Testing Hardcoded Values Replacement...\n');

  try {
    // Initialize database connection
    await initTestDb();

    // Test 1: Scan files for remaining hardcoded values
    console.log('📋 Test 1: Scanning files for remaining hardcoded values');
    
    let totalFindings = 0;
    const allFindings: { [key: string]: any[] } = {};

    for (const filePath of FILES_TO_SCAN) {
      console.log(`\n  Scanning: ${filePath}`);
      const findings = scanFileForHardcodedValues(filePath);
      
      if (findings.length > 0) {
        allFindings[filePath] = findings;
        totalFindings += findings.length;
        
        console.log(`    ❌ Found ${findings.length} potential hardcoded values:`);
        findings.forEach(finding => {
          console.log(`      Line ${finding.line}: ${finding.pattern}`);
          console.log(`        ${finding.content}`);
        });
      } else {
        console.log(`    ✅ No hardcoded values found`);
      }
    }

    console.log(`\n  📊 Total potential hardcoded values found: ${totalFindings}`);

    // Test 2: Check database settings existence
    console.log('\n📋 Test 2: Checking database settings existence');
    
    const { missing, existing } = await checkDatabaseSettings();
    
    console.log(`\n  ✅ Existing settings: ${existing.length}/${EXPECTED_SETTINGS.length}`);
    console.log(`  ❌ Missing settings: ${missing.length}/${EXPECTED_SETTINGS.length}`);

    if (missing.length > 0) {
      console.log('\n  Missing settings:');
      missing.forEach(setting => {
        console.log(`    - ${setting.category}:${setting.key}`);
      });
    }

    // Test 3: Verify settings service usage
    console.log('\n📋 Test 3: Verifying settings service usage in key files');

    const keyFiles = [
      '../services/settingsService.ts',
      '../services/settingsInitializationService.ts',
      '../middleware/security.ts',
      '../middleware/auth.ts',
    ];

    for (const filePath of keyFiles) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const hasSettingsUsage = content.includes('getSetting') || content.includes('settingsService');
        console.log(`    ${filePath}: ${hasSettingsUsage ? '✅' : '❌'} Uses settings service`);
      } else {
        console.log(`    ${filePath}: ⚠️  File not found`);
      }
    }

    // Test 4: Check for dynamic validation usage
    console.log('\n📋 Test 4: Checking dynamic validation usage');

    const validationFiles = [
      '../middleware/dynamicValidation.ts',
      '../routes/admin.ts',
    ];

    for (const filePath of validationFiles) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const hasDynamicValidation = content.includes('createAmountValidation') || 
                                   content.includes('createPaginationValidation') ||
                                   content.includes('dynamicValidationMiddleware');
        console.log(`    ${filePath}: ${hasDynamicValidation ? '✅' : '❌'} Uses dynamic validation`);
      } else {
        console.log(`    ${filePath}: ⚠️  File not found`);
      }
    }

    // Test 5: Sample settings retrieval
    console.log('\n📋 Test 5: Sample settings retrieval test');
    
    const sampleSettings = [
      { category: 'rate_limiting', key: 'general_max_requests' },
      { category: 'security', key: 'jwt_access_expires_in' },
      { category: 'business_rules', key: 'min_order_amount' },
      { category: 'pagination', key: 'max_limit' },
    ];

    for (const setting of sampleSettings) {
      try {
        const result = await testDb.query(
          'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
          [setting.category, setting.key]
        );

        if (result.rows.length > 0) {
          const { setting_value, data_type } = result.rows[0];
          console.log(`    ✅ ${setting.category}:${setting.key} = ${setting_value} (${data_type})`);
        } else {
          console.log(`    ❌ ${setting.category}:${setting.key} not found`);
        }
      } catch (error) {
        console.log(`    ❌ ${setting.category}:${setting.key} error: ${error}`);
      }
    }

    console.log('\n🎉 Hardcoded values replacement test completed!');
    console.log('\n📊 Test Summary:');
    console.log(`  📁 Files scanned: ${FILES_TO_SCAN.length}`);
    console.log(`  🔍 Potential hardcoded values found: ${totalFindings}`);
    console.log(`  ✅ Database settings existing: ${existing.length}/${EXPECTED_SETTINGS.length}`);
    console.log(`  ❌ Database settings missing: ${missing.length}/${EXPECTED_SETTINGS.length}`);
    
    if (totalFindings === 0 && missing.length === 0) {
      console.log('  🎉 All hardcoded values appear to be replaced with database settings!');
    } else {
      console.log('  ⚠️  Some hardcoded values or missing settings detected - review needed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Close database connection
    if (testDb) {
      await testDb.end();
      console.log('✅ Database connection closed');
    }
  }
};

// Run the test
if (require.main === module) {
  testHardcodedValuesReplacement().then(() => {
    console.log('\n✅ Hardcoded values replacement test script completed');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Hardcoded values replacement test script failed:', error);
    process.exit(1);
  });
}

export { testHardcodedValuesReplacement };
