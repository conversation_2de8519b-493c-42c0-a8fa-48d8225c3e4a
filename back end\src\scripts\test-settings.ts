#!/usr/bin/env ts-node

/**
 * Settings Service Diagnostic Script
 *
 * This script tests the settings service to diagnose why saved settings
 * are not being applied in the application.
 */

import dotenv from 'dotenv';

// Load environment variables BEFORE importing anything that uses them
dotenv.config();

import { Pool } from 'pg';
import { settingsService } from '../services/settingsService';
import { logger } from '../config/logger';
import { executeQuery, connectDatabases } from '../config/database';

// Direct database configuration (matching other scripts)
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

const testSettings = async () => {
  const db = new Pool(dbConfig);

  try {
    console.log('🔍 Testing Settings Service...');

    // First, ensure database connections are established
    console.log('🔌 Establishing database connections...');
    await connectDatabases();
    console.log('✅ Database connections established');

    // Test database connection
    await db.query('SELECT NOW()');
    console.log('✅ Direct database connection successful');

    // Check if system_settings table exists and has data
    console.log('\n📊 Checking system_settings table...');
    const tableCheck = await db.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_name = 'system_settings'
    `);
    
    if (tableCheck.rows[0].count === '0') {
      console.log('❌ system_settings table does not exist!');
      return;
    }
    
    console.log('✅ system_settings table exists');

    // Check total settings count
    const countResult = await db.query('SELECT COUNT(*) as count FROM system_settings');
    const totalSettings = countResult.rows[0].count;
    console.log(`📈 Total settings in database: ${totalSettings}`);

    // Check payment settings specifically
    console.log('\n💰 Checking payment settings in database...');
    const paymentSettingsResult = await db.query(`
      SELECT category, setting_key, setting_value, data_type 
      FROM system_settings 
      WHERE category = 'payment' 
      ORDER BY setting_key
    `);
    
    console.log('Payment settings in database:');
    paymentSettingsResult.rows.forEach(row => {
      console.log(`  ${row.setting_key}: ${row.setting_value} (${row.data_type})`);
    });

    // Test the executeQuery function that settings service uses
    console.log('\n🔧 Testing executeQuery function (used by SettingsService)...');
    try {
      const testResult = await executeQuery(
        'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
        ['payment', 'min_order_amount']
      );
      console.log('✅ executeQuery works:', testResult.rows);
    } catch (error) {
      console.log('❌ executeQuery failed:', error);
    }

    // Test settings service directly
    console.log('\n🔧 Testing SettingsService...');

    // Clear cache first
    settingsService.clearCache();
    console.log('✅ Cache cleared');

    // Test individual setting retrieval
    console.log('\n🔍 Testing individual setting retrieval...');
    const minAmount = await settingsService.getSetting('payment', 'min_order_amount', 0.01);
    console.log(`min_order_amount: ${minAmount} (type: ${typeof minAmount})`);
    
    const maxAmount = await settingsService.getSetting('payment', 'max_order_amount', 1000000);
    console.log(`max_order_amount: ${maxAmount} (type: ${typeof maxAmount})`);
    
    const timeoutMinutes = await settingsService.getSetting('payment', 'payment_timeout_minutes', 30);
    console.log(`payment_timeout_minutes: ${timeoutMinutes} (type: ${typeof timeoutMinutes})`);

    // Test getPaymentSettings method
    console.log('\n💳 Testing getPaymentSettings method...');
    const paymentSettings = await settingsService.getPaymentSettings();
    console.log('Payment settings from service:', JSON.stringify(paymentSettings, null, 2));

    // Test cache functionality
    console.log('\n🗄️ Testing cache functionality...');
    const startTime = Date.now();
    await settingsService.getPaymentSettings();
    const cachedTime = Date.now() - startTime;
    console.log(`Second call (should be cached): ${cachedTime}ms`);

    // Test getAllSettings
    console.log('\n📋 Testing getAllSettings...');
    const allSettings = await settingsService.getAllSettings();
    console.log('All settings categories:', Object.keys(allSettings));
    if (allSettings.payment) {
      console.log('Payment settings from getAllSettings:', allSettings.payment);
    }

    // Test a specific scenario: simulate order creation validation
    console.log('\n🛒 Simulating order creation validation...');
    const testAmount = 1.0;
    const settings = await settingsService.getPaymentSettings();
    
    console.log(`Testing order amount: ${testAmount}`);
    console.log(`Min amount from settings: ${settings.minOrderAmount}`);
    console.log(`Max amount from settings: ${settings.maxOrderAmount}`);
    
    if (testAmount < settings.minOrderAmount) {
      console.log(`❌ Order would be rejected: Amount ${testAmount} is less than minimum ${settings.minOrderAmount}`);
    } else if (testAmount > settings.maxOrderAmount) {
      console.log(`❌ Order would be rejected: Amount ${testAmount} is greater than maximum ${settings.maxOrderAmount}`);
    } else {
      console.log(`✅ Order would be accepted: Amount ${testAmount} is within limits`);
    }

    console.log('\n🎉 Settings test completed successfully!');

  } catch (error) {
    console.error('❌ Settings test failed:', error);
  } finally {
    await db.end();
  }
};

// Run the test
testSettings().catch(console.error);
