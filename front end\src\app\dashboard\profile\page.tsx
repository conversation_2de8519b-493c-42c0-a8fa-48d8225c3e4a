'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Edit,
  Save,
  X,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

interface ProfileFormData {
  email: string;
  telegramId: string;
  firstName: string;
  lastName: string;
  phone: string;
  dateOfBirth: string;
}

export default function ProfilePage() {
  const { user, loading } = useAuth();
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    defaultValues: {
      email: user?.email || '',
      telegramId: user?.telegramId || '',
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      phone: user?.phone || '',
      dateOfBirth: user?.dateOfBirth || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    setSaving(true);
    try {
      // TODO: Replace with actual API call
      console.log('Updating profile:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Profile updated successfully!');
      setEditing(false);
    } catch (error) {
      toast.error('Failed to update profile');
      console.error('Profile update error:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    reset();
    setEditing(false);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="spinner-lg mx-auto mb-4" />
            <p className="text-gray-600">Loading profile...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
            <p className="text-gray-600">Manage your account information and preferences</p>
          </div>
          {!editing && (
            <button
              onClick={() => setEditing(true)}
              className="btn-outline inline-flex items-center"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Form */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Personal Information</h2>
                <p className="text-sm text-gray-600">Update your personal details and contact information</p>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <input
                        type="text"
                        disabled={!editing}
                        className={`input ${!editing ? 'bg-gray-50' : ''}`}
                        {...register('firstName', {
                          required: editing ? 'First name is required' : false,
                        })}
                      />
                      {errors.firstName && (
                        <p className="text-sm text-error-600 mt-1">{errors.firstName.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <input
                        type="text"
                        disabled={!editing}
                        className={`input ${!editing ? 'bg-gray-50' : ''}`}
                        {...register('lastName', {
                          required: editing ? 'Last name is required' : false,
                        })}
                      />
                      {errors.lastName && (
                        <p className="text-sm text-error-600 mt-1">{errors.lastName.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        disabled={!editing}
                        className={`input ${!editing ? 'bg-gray-50' : ''}`}
                        {...register('email', {
                          required: editing ? 'Email is required' : false,
                          pattern: editing ? {
                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                            message: 'Invalid email address',
                          } : undefined,
                        })}
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        {user?.emailVerified ? (
                          <CheckCircle className="h-5 w-5 text-success-600" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-warning-600" />
                        )}
                      </div>
                    </div>
                    {errors.email && (
                      <p className="text-sm text-error-600 mt-1">{errors.email.message}</p>
                    )}
                    {!user?.emailVerified && (
                      <p className="text-sm text-warning-600 mt-1">
                        Email not verified. <button className="underline">Resend verification</button>
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Telegram ID
                    </label>
                    <input
                      type="text"
                      disabled={true}
                      className="input bg-gray-50"
                      {...register('telegramId')}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Telegram ID cannot be changed after registration
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      disabled={!editing}
                      className={`input ${!editing ? 'bg-gray-50' : ''}`}
                      {...register('phone')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      disabled={!editing}
                      className={`input ${!editing ? 'bg-gray-50' : ''}`}
                      {...register('dateOfBirth')}
                    />
                  </div>

                  {editing && (
                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="btn-outline flex-1"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={saving}
                        className="btn-primary flex-1"
                      >
                        {saving ? (
                          <>
                            <div className="spinner-sm mr-2" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>

          {/* Profile Summary */}
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Account Summary</h2>
              </div>
              <div className="card-body space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-ton-100 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-ton-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {user?.firstName && user?.lastName 
                        ? `${user.firstName} ${user.lastName}`
                        : user?.telegramId || 'User'}
                    </p>
                    <p className="text-sm text-gray-600">{user?.email}</p>
                  </div>
                </div>

                <div className="space-y-3 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Email Status</span>
                    </div>
                    <span className={`text-sm font-medium ${
                      user?.emailVerified ? 'text-success-600' : 'text-warning-600'
                    }`}>
                      {user?.emailVerified ? 'Verified' : 'Unverified'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Member Since</span>
                    </div>
                    <span className="text-sm text-gray-900">
                      {user?.createdAt 
                        ? new Date(user.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric',
                          })
                        : 'N/A'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Account Type</span>
                    </div>
                    <span className="text-sm text-gray-900 capitalize">
                      {user?.role || 'User'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
              </div>
              <div className="card-body space-y-3">
                <button className="w-full text-left p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">Change Password</span>
                    <span className="text-gray-400">→</span>
                  </div>
                </button>
                <button className="w-full text-left p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">Two-Factor Authentication</span>
                    <span className="text-gray-400">→</span>
                  </div>
                </button>
                <button className="w-full text-left p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">Download Data</span>
                    <span className="text-gray-400">→</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
