/**
 * Test script for dynamic validation integration
 * 
 * This script tests that the dynamic validation middleware can successfully
 * load settings from the database and create appropriate validation rules.
 */

const { Pool } = require('pg');

// Database configuration - hardcoded for local development
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'tonsite_dev',
  password: '1234',
  port: 5432,
  ssl: false
});

async function testDynamicValidation() {
  console.log('🧪 Testing Dynamic Validation Integration...\n');
  console.log('Database config:', {
    host: 'localhost',
    database: 'tonsite_dev',
    user: 'postgres',
    port: 5432
  });

  try {
    // Test 0: Basic connection test
    console.log('\n0. Testing database connection...');
    const connectionTest = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connection successful:', connectionTest.rows[0].current_time);

    // Test 1: Check payment settings for currency validation
    console.log('\n1. Checking payment settings for currency validation...');
    const paymentSettings = await pool.query(
      "SELECT setting_key, setting_value FROM system_settings WHERE category = 'payment' ORDER BY setting_key"
    );

    if (paymentSettings.rows.length === 0) {
      console.log('❌ No payment settings found in database');
      console.log('   Make sure payment settings are configured');
      return false;
    }

    console.log('✅ Found payment settings:');
    paymentSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value}`);
    });

    // Test 2: Check voucher settings for quantity/expiry validation
    console.log('\n2. Checking voucher settings for validation...');
    const voucherSettings = await pool.query(
      "SELECT setting_key, setting_value FROM system_settings WHERE category = 'voucher' ORDER BY setting_key"
    );

    if (voucherSettings.rows.length === 0) {
      console.log('❌ No voucher settings found in database');
      console.log('   Make sure voucher settings are configured');
      return false;
    }

    console.log('✅ Found voucher settings:');
    voucherSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value}`);
    });

    // Test 3: Verify specific settings needed for dynamic validation
    console.log('\n3. Verifying required settings for dynamic validation...');
    
    const requiredSettings = [
      { category: 'payment', key: 'supported_currencies', description: 'Currency validation' },
      { category: 'payment', key: 'min_order_amount', description: 'Minimum amount validation' },
      { category: 'payment', key: 'max_order_amount', description: 'Maximum amount validation' },
      { category: 'voucher', key: 'max_vouchers_per_order', description: 'Voucher quantity validation' },
      { category: 'voucher', key: 'default_expiry_days', description: 'Expiry days validation' },
    ];

    let allSettingsPresent = true;
    for (const setting of requiredSettings) {
      const result = await pool.query(
        'SELECT setting_value FROM system_settings WHERE category = $1 AND setting_key = $2',
        [setting.category, setting.key]
      );

      if (result.rows.length > 0) {
        console.log(`✅ ${setting.description}: ${result.rows[0].setting_value}`);
      } else {
        console.log(`❌ Missing setting: ${setting.category}.${setting.key} (${setting.description})`);
        allSettingsPresent = false;
      }
    }

    if (!allSettingsPresent) {
      console.log('\n❌ Some required settings are missing. Dynamic validation may not work properly.');
      return false;
    }

    // Test 4: Test that settings can be parsed correctly
    console.log('\n4. Testing settings parsing...');
    
    const currenciesResult = await pool.query(
      "SELECT setting_value FROM system_settings WHERE category = 'payment' AND setting_key = 'supported_currencies'"
    );
    
    if (currenciesResult.rows.length > 0) {
      try {
        const currencies = JSON.parse(currenciesResult.rows[0].setting_value);
        if (Array.isArray(currencies)) {
          console.log('✅ Supported currencies parsed successfully:', currencies);
        } else {
          console.log('❌ Supported currencies is not an array:', currencies);
          return false;
        }
      } catch (error) {
        console.log('❌ Failed to parse supported currencies:', error.message);
        return false;
      }
    }

    // Test 5: Test numeric settings
    const minAmountResult = await pool.query(
      "SELECT setting_value FROM system_settings WHERE category = 'payment' AND setting_key = 'min_order_amount'"
    );
    
    if (minAmountResult.rows.length > 0) {
      const minAmount = parseFloat(minAmountResult.rows[0].setting_value);
      if (!isNaN(minAmount) && minAmount > 0) {
        console.log('✅ Minimum order amount parsed successfully:', minAmount);
      } else {
        console.log('❌ Invalid minimum order amount:', minAmountResult.rows[0].setting_value);
        return false;
      }
    }

    console.log('\n✅ Dynamic Validation Integration Test PASSED');
    console.log('   The dynamic validation middleware should now be able to load');
    console.log('   validation rules from the database instead of using hardcoded values.');
    
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', {
      name: error.name,
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused - make sure PostgreSQL is running on localhost:5432');
      console.log('   On Windows, you can start PostgreSQL service from Services or pgAdmin');
    } else if (error.code === '28P01') {
      console.log('\n💡 Authentication failed - check database credentials');
      console.log('   Make sure user "postgres" exists with password "1234"');
      console.log('   Or update the credentials in this test script');
    } else if (error.code === '3D000') {
      console.log('\n💡 Database "tonsite_dev" does not exist');
      console.log('   Create the database first or check the database name');
    }
    
    return false;
  } finally {
    try {
      await pool.end();
    } catch (closeError) {
      console.error('Error closing pool:', closeError.message);
    }
  }
}

// Run the test
testDynamicValidation()
  .then(success => {
    if (success) {
      console.log('\n🎉 All tests passed! Dynamic validation is ready to use.');
      process.exit(0);
    } else {
      console.log('\n❌ Tests failed. Please fix the issues before proceeding.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
