import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery } from '../config/database';
import {
  logger,
  logAuthEvent,
  logSecurityEvent,
  logErrorWithRequest,
  logBusinessError,
  logValidationError
} from '../config/logger';
import {
  generateTokens,
  hashPassword,
  verifyPassword,
  handleFailedLogin,
  handleSuccessfulLogin,
  blacklistToken,
  createUserSession,
  verifyToken,
} from '../middleware/auth';
import { generateSecureToken } from '../utils/helpers';
import { sendEmail } from '../services/emailService';
import { settingsService } from '../services/settingsService';

// Register new user
export const register = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { email, telegramId, password } = req.body;

    // Check if user already exists
    const existingUser = await executeQuery(
      'SELECT id, email, telegram_id FROM users WHERE email = $1 OR telegram_id = $2',
      [email, telegramId]
    );

    if (existingUser.rows.length > 0) {
      const existing = existingUser.rows[0];
      if (existing.email === email) {
        return res.status(409).json({
          success: false,
          error: 'Email address already registered',
        });
      }
      if (existing.telegram_id === telegramId) {
        return res.status(409).json({
          success: false,
          error: 'Telegram ID already registered',
        });
      }
    }

    // Hash password
    const passwordHash = await hashPassword(password);
    
    // Generate email verification token with dynamic expiry from database settings
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();
    const emailVerificationToken = generateSecureToken(32);
    const emailVerificationExpires = new Date(Date.now() + businessRuleSettings.emailVerificationExpiresHours * 60 * 60 * 1000);

    // Create user
    const userId = uuidv4();
    const result = await executeQuery(
      `INSERT INTO users (id, email, telegram_id, password_hash, email_verification_token, email_verification_expires)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING id, email, telegram_id, email_verified, role, created_at`,
      [userId, email, telegramId, passwordHash, emailVerificationToken, emailVerificationExpires]
    );

    const user = result.rows[0];

    // Send verification email
    try {
      await sendEmail({
        to: email,
        subject: 'Verify Your Email Address',
        template: 'email-verification',
        data: {
          userName: telegramId,
          verificationLink: `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify-email?token=${emailVerificationToken}`,
          expiresIn: '24 hours',
        },
      });
    } catch (emailError) {
      logger.error('Failed to send verification email:', emailError);
      // Don't fail registration if email fails
    }

    logAuthEvent('USER_REGISTERED', user.id, true, req);

    return res.status(201).json({
      success: true,
      message: 'Registration successful. Please check your email to verify your account.',
      data: {
        user: {
          id: user.id,
          email: user.email,
          telegramId: user.telegram_id,
          emailVerified: user.email_verified,
          role: user.role,
          createdAt: user.created_at,
        },
      },
    });
  } catch (error) {
    const errorId = logBusinessError(
      'USER_REGISTRATION',
      error as Error,
      {
        email: req.body.email,
        telegramId: req.body.telegramId,
        registrationAttempt: true
      },
      req
    );

    return res.status(500).json({
      success: false,
      error: 'Registration failed',
      errorId,
    });
  }
};

// Login user
export const login = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { email, password } = req.body;

    // Get user from database
    const userResult = await executeQuery(
      'SELECT id, email, telegram_id, password_hash, email_verified, role, failed_login_attempts, locked_until FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      await handleFailedLogin(email, req);
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password',
      });
    }

    const user = userResult.rows[0];

    // Check if account is locked
    if (user.locked_until && new Date(user.locked_until) > new Date()) {
      logSecurityEvent('LOCKED_ACCOUNT_LOGIN_ATTEMPT', { email }, req);
      return res.status(423).json({
        success: false,
        error: 'Account is temporarily locked. Please try again later.',
      });
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      await handleFailedLogin(email, req);
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password',
      });
    }

    // Check if email is verified
    if (!user.email_verified) {
      return res.status(403).json({
        success: false,
        error: 'Please verify your email address before logging in',
        code: 'EMAIL_NOT_VERIFIED',
      });
    }

    // Generate tokens
    const userForToken = {
      id: user.id,
      email: user.email,
      telegramId: user.telegram_id,
      emailVerified: user.email_verified,
      role: user.role,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    };

    const { accessToken, refreshToken } = await generateTokens(userForToken);

    // Create session
    const sessionToken = generateSecureToken(32);
    await createUserSession(user.id, sessionToken, refreshToken, req);

    // Handle successful login
    await handleSuccessfulLogin(email, req);

    return res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          telegramId: user.telegram_id,
          emailVerified: user.email_verified,
          role: user.role,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    logger.error('Login error:', error);
    return res.status(500).json({
      success: false,
      error: 'Login failed',
    });
  }
};

// Logout user
export const logout = async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Blacklist the token
      await blacklistToken(token, 15 * 60); // 15 minutes (token expiry)
      
      // Invalidate session if session token is provided
      const sessionToken = req.headers['x-session-token'] as string;
      if (sessionToken) {
        await executeQuery(
          'UPDATE user_sessions SET is_active = false WHERE session_token = $1',
          [sessionToken]
        );
      }
    }

    logAuthEvent('USER_LOGOUT', req.user?.id || 'unknown', true, req);

    res.json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Logout failed',
    });
  }
};

// Verify email
export const verifyEmail = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: 'Verification token is required',
      });
    }

    // Find user with this verification token
    const userResult = await executeQuery(
      'SELECT id, email, email_verification_expires FROM users WHERE email_verification_token = $1',
      [token]
    );

    if (userResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid verification token',
      });
    }

    const user = userResult.rows[0];

    // Check if token has expired
    if (new Date() > new Date(user.email_verification_expires)) {
      return res.status(400).json({
        success: false,
        error: 'Verification token has expired',
        code: 'TOKEN_EXPIRED',
      });
    }

    // Update user as verified
    await executeQuery(
      'UPDATE users SET email_verified = true, email_verification_token = NULL, email_verification_expires = NULL WHERE id = $1',
      [user.id]
    );

    logAuthEvent('EMAIL_VERIFIED', user.id, true, req);

    return res.json({
      success: true,
      message: 'Email verified successfully',
    });
  } catch (error) {
    logger.error('Email verification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Email verification failed',
    });
  }
};

// Resend verification email
export const resendVerification = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { email } = req.body;

    // Find user
    const userResult = await executeQuery(
      'SELECT id, email, telegram_id, email_verified FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    if (user.email_verified) {
      return res.status(400).json({
        success: false,
        error: 'Email is already verified',
      });
    }

    // Generate new verification token with dynamic expiry from database settings
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();
    const emailVerificationToken = generateSecureToken(32);
    const emailVerificationExpires = new Date(Date.now() + businessRuleSettings.emailVerificationExpiresHours * 60 * 60 * 1000);

    // Update user with new token
    await executeQuery(
      'UPDATE users SET email_verification_token = $1, email_verification_expires = $2 WHERE id = $3',
      [emailVerificationToken, emailVerificationExpires, user.id]
    );

    // Send verification email
    await sendEmail({
      to: email,
      subject: 'Verify Your Email Address',
      template: 'email-verification',
      data: {
        userName: user.telegram_id,
        verificationLink: `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify-email?token=${emailVerificationToken}`,
        expiresIn: '24 hours',
      },
    });

    return res.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error) {
    logger.error('Resend verification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to resend verification email',
    });
  }
};

// Request password reset
export const requestPasswordReset = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { email } = req.body;

    // Find user
    const userResult = await executeQuery(
      'SELECT id, email, telegram_id FROM users WHERE email = $1',
      [email]
    );

    // Always return success to prevent email enumeration
    if (userResult.rows.length === 0) {
      return res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
    }

    const user = userResult.rows[0];

    // Generate password reset token with dynamic expiry from database settings
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();
    const passwordResetToken = generateSecureToken(32);
    const passwordResetExpires = new Date(Date.now() + businessRuleSettings.passwordResetExpiresHours * 60 * 60 * 1000);

    // Update user with reset token
    await executeQuery(
      'UPDATE users SET password_reset_token = $1, password_reset_expires = $2 WHERE id = $3',
      [passwordResetToken, passwordResetExpires, user.id]
    );

    // Send password reset email
    try {
      await sendEmail({
        to: email,
        subject: 'Reset Your Password',
        template: 'password-reset',
        data: {
          userName: user.telegram_id,
          resetLink: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${passwordResetToken}`,
          expiresIn: '1 hour',
        },
      });
    } catch (emailError) {
      logger.error('Failed to send password reset email:', emailError);
    }

    logSecurityEvent('PASSWORD_RESET_REQUESTED', { email }, req);

    return res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
    });
  } catch (error) {
    logger.error('Password reset request error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to process password reset request',
    });
  }
};

// Reset password
export const resetPassword = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Reset token and new password are required',
      });
    }

    // Find user with this reset token
    const userResult = await executeQuery(
      'SELECT id, email, password_reset_expires FROM users WHERE password_reset_token = $1',
      [token]
    );

    if (userResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid reset token',
      });
    }

    const user = userResult.rows[0];

    // Check if token has expired
    if (new Date() > new Date(user.password_reset_expires)) {
      return res.status(400).json({
        success: false,
        error: 'Reset token has expired',
        code: 'TOKEN_EXPIRED',
      });
    }

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update password and clear reset token
    await executeQuery(
      `UPDATE users SET
        password_hash = $1,
        password_reset_token = NULL,
        password_reset_expires = NULL,
        failed_login_attempts = 0,
        locked_until = NULL
       WHERE id = $2`,
      [passwordHash, user.id]
    );

    // Invalidate all existing sessions for this user
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE user_id = $1',
      [user.id]
    );

    logSecurityEvent('PASSWORD_RESET_COMPLETED', { userId: user.id }, req);

    return res.json({
      success: true,
      message: 'Password reset successfully',
    });
  } catch (error) {
    logger.error('Password reset error:', error);
    return res.status(500).json({
      success: false,
      error: 'Password reset failed',
    });
  }
};

// Refresh token
export const refreshToken = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: 'Refresh token is required',
      });
    }

    // Verify refresh token
    const payload = verifyToken(refreshToken);
    if (!payload) {
      return res.status(401).json({
        success: false,
        error: 'Invalid refresh token',
      });
    }

    // Check if session exists and is active
    const sessionResult = await executeQuery(
      'SELECT id FROM user_sessions WHERE refresh_token = $1 AND is_active = true AND expires_at > CURRENT_TIMESTAMP',
      [refreshToken]
    );

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired refresh token',
      });
    }

    // Get user
    const userResult = await executeQuery(
      'SELECT id, email, telegram_id, email_verified, role, created_at, updated_at FROM users WHERE id = $1',
      [payload.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Generate new tokens
    const userForToken = {
      id: user.id,
      email: user.email,
      telegramId: user.telegram_id,
      emailVerified: user.email_verified,
      role: user.role,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    };

    const { accessToken, refreshToken: newRefreshToken } = await generateTokens(userForToken);

    // Update session with new refresh token
    await executeQuery(
      'UPDATE user_sessions SET refresh_token = $1 WHERE refresh_token = $2',
      [newRefreshToken, refreshToken]
    );

    return res.json({
      success: true,
      data: {
        accessToken,
        refreshToken: newRefreshToken,
      },
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    return res.status(500).json({
      success: false,
      error: 'Token refresh failed',
    });
  }
};