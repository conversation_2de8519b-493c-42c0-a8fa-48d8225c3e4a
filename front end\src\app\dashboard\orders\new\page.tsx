'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import {
  ArrowLeft,
  ShoppingCart,
  CreditCard,
  Gift,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useTonConnect } from '@/contexts/TonConnectContext';
import { shopApi, ordersApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

interface OrderFormData {
  productId: string;
  amount: string;
  quantity: number;
  paymentMethod: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryName: string;
  categoryIcon: string;
  minAmount: number;
  maxAmount: number;
  currency: string;
  rating: number;
  reviewCount: number;
  image: string;
  popular: boolean;
  available: boolean;
  features: string[];
  metadata: any;
  createdAt: string;
}

export default function NewOrderPage() {
  const { user } = useAuth();
  const { connected, wallet } = useTonConnect();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [productsLoading, setProductsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<OrderFormData>({
    defaultValues: {
      quantity: 1,
      paymentMethod: 'ton',
    },
  });

  const watchedAmount = watch('amount');
  const watchedQuantity = watch('quantity');

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setProductsLoading(true);
        const response = await shopApi.getProducts({ available: true });
        if (response.success) {
          setProducts(response.data.products);
        } else {
          toast.error('Failed to load products');
        }
      } catch (error) {
        console.error('Failed to load products:', error);
        toast.error('Failed to load products');
      } finally {
        setProductsLoading(false);
      }
    };

    loadProducts();
  }, []);

  const calculateTotal = () => {
    const amount = parseFloat(watchedAmount || '0');
    const quantity = parseInt(watchedQuantity?.toString() || '1');
    return (amount * quantity).toFixed(2);
  };

  const onSubmit = async (data: OrderFormData) => {
    if (!selectedProduct) {
      toast.error('Please select a product');
      return;
    }

    setLoading(true);
    try {
      const orderData = {
        productId: selectedProduct.id,
        amount: parseFloat(data.amount),
        currency: selectedProduct.currency,
        quantity: data.quantity,
        memo: `Order for ${selectedProduct.name}`,
      };

      const response = await ordersApi.create(orderData);

      if (response.success) {
        toast.success('Order created successfully!');
        router.push(`/dashboard/orders/${response.data.order.id}`);
      } else {
        toast.error(response.error || 'Failed to create order');
      }
    } catch (error: any) {
      console.error('Failed to create order:', error);
      toast.error(error.response?.data?.error || 'Failed to create order');
    } finally {
      setLoading(false);
    }
  };

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setValue('productId', product.id);
    setValue('amount', product.minAmount.toString());
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link
            href="/dashboard/orders"
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Order</h1>
            <p className="text-gray-600">Purchase premium game vouchers with TON</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Selection */}
          <div className="lg:col-span-2 space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Select Product</h2>
                <p className="text-sm text-gray-600">Choose the product you want to purchase</p>
              </div>
              <div className="card-body">
                {productsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-ton-600" />
                    <span className="ml-2 text-gray-600">Loading products...</span>
                  </div>
                ) : products.length === 0 ? (
                  <div className="text-center py-8">
                    <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No products available at the moment</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {products.map((product) => (
                      <div
                        key={product.id}
                        className={`relative p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedProduct?.id === product.id
                            ? 'border-ton-500 bg-ton-50'
                            : product.available
                            ? 'border-gray-200 hover:border-ton-300'
                            : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                        }`}
                        onClick={() => product.available && handleProductSelect(product)}
                      >
                        {!product.available && (
                          <div className="absolute inset-0 bg-gray-100 bg-opacity-75 rounded-lg flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-500">Unavailable</span>
                          </div>
                        )}
                        <div className="flex items-start gap-3">
                          <Gift className={`h-6 w-6 mt-1 ${
                            selectedProduct?.id === product.id ? 'text-ton-600' : 'text-gray-400'
                          }`} />
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{product.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <p className="text-xs text-gray-500">
                                {product.minAmount} - {product.maxAmount} {product.currency}
                              </p>
                              {product.rating > 0 && (
                                <span className="text-xs text-yellow-600">
                                  ★ {product.rating.toFixed(1)}
                                </span>
                              )}
                            </div>
                          </div>
                          {selectedProduct?.id === product.id && (
                            <CheckCircle className="h-5 w-5 text-ton-600" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Order Details */}
            {selectedProduct && (
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-medium text-gray-900">Order Details</h2>
                </div>
                <div className="card-body">
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    {/* Hidden product ID field */}
                    <input type="hidden" {...register('productId')} />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Amount ({selectedProduct.currency})
                        </label>
                        <input
                          type="number"
                          min={selectedProduct.minAmount}
                          max={selectedProduct.maxAmount}
                          step="0.01"
                          className="input"
                          {...register('amount', {
                            required: 'Amount is required',
                            min: {
                              value: selectedProduct.minAmount,
                              message: `Minimum amount is ${selectedProduct.minAmount} ${selectedProduct.currency}`,
                            },
                            max: {
                              value: selectedProduct.maxAmount,
                              message: `Maximum amount is ${selectedProduct.maxAmount} ${selectedProduct.currency}`,
                            },
                          })}
                        />
                        {errors.amount && (
                          <p className="text-sm text-error-600 mt-1">{errors.amount.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quantity
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          className="input"
                          {...register('quantity', {
                            required: 'Quantity is required',
                            min: { value: 1, message: 'Minimum quantity is 1' },
                            max: { value: 10, message: 'Maximum quantity is 10' },
                          })}
                        />
                        {errors.quantity && (
                          <p className="text-sm text-error-600 mt-1">{errors.quantity.message}</p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Payment Method
                      </label>
                      <select
                        className="input"
                        {...register('paymentMethod', { required: 'Payment method is required' })}
                      >
                        <option value="ton">TON Cryptocurrency</option>
                      </select>
                      {errors.paymentMethod && (
                        <p className="text-sm text-error-600 mt-1">{errors.paymentMethod.message}</p>
                      )}
                    </div>

                    {/* Wallet Connection Warning */}
                    {!connected && (
                      <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-warning-600 mr-2" />
                          <span className="text-sm text-warning-800">
                            Please connect your TON wallet to proceed with payment
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="flex gap-3 pt-4">
                      <Link
                        href="/dashboard/orders"
                        className="btn-outline flex-1"
                      >
                        Cancel
                      </Link>
                      <button
                        type="submit"
                        disabled={loading || !connected}
                        className="btn-primary flex-1 inline-flex items-center justify-center"
                      >
                        {loading ? (
                          <>
                            <div className="spinner-sm mr-2" />
                            Creating Order...
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Create Order
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
              </div>
              <div className="card-body">
                {selectedProduct ? (
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Product:</span>
                      <span className="text-sm font-medium">{selectedProduct.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Category:</span>
                      <span className="text-sm font-medium">{selectedProduct.categoryName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Amount:</span>
                      <span className="text-sm font-medium">
                        {watchedAmount || '0'} {selectedProduct.currency}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Quantity:</span>
                      <span className="text-sm font-medium">{watchedQuantity || 1}</span>
                    </div>
                    <hr />
                    <div className="flex justify-between">
                      <span className="font-medium">Total:</span>
                      <span className="font-bold text-ton-600">
                        {calculateTotal()} {selectedProduct.currency}
                      </span>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 text-center py-4">
                    Select a product to see order summary
                  </p>
                )}
              </div>
            </div>

            {/* Wallet Info */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Payment Wallet</h2>
              </div>
              <div className="card-body">
                {connected && wallet ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-ton-600" />
                      <span className="text-sm font-medium">Connected</span>
                    </div>
                    <p className="text-xs text-gray-600 font-mono">
                      {wallet.address?.slice(0, 8)}...{wallet.address?.slice(-8)}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    Connect your TON wallet to proceed
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
