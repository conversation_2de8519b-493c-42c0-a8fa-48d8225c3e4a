import { Pool } from 'pg';
import { body, query, Validation<PERSON>hain } from 'express-validator';

// Hardcoded credentials for local dev testing
const DB_CONFIG = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

// Test database connection
let testDb: Pool;

// Initialize test database connection
const initTestDb = async () => {
  testDb = new Pool(DB_CONFIG);
  await testDb.query('SELECT NOW()');
  console.log('✅ Test database connection established');
};

// Test Settings Service for validation
class TestSettingsService {
  async getSetting(category: string, key: string, defaultValue: any): Promise<any> {
    try {
      const result = await testDb.query(
        'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
        [category, key]
      );

      if (result.rows.length === 0) {
        return defaultValue;
      }

      const { setting_value, data_type } = result.rows[0];
      let parsedValue = setting_value;

      switch (data_type) {
        case 'number':
          parsedValue = parseFloat(setting_value);
          break;
        case 'boolean':
          parsedValue = setting_value === 'true';
          break;
        case 'json':
          parsedValue = JSON.parse(setting_value);
          break;
        default:
          parsedValue = setting_value;
      }

      return parsedValue;
    } catch (error) {
      console.error(`Error getting setting ${category}:${key}:`, error);
      return defaultValue;
    }
  }

  async getBusinessRuleSettings() {
    return {
      minOrderAmount: await this.getSetting('business_rules', 'min_order_amount', '0.01'),
      maxOrderAmount: await this.getSetting('business_rules', 'max_order_amount', '1000000'),
    };
  }

  async getPaginationSettings() {
    return {
      defaultPage: await this.getSetting('pagination', 'default_page', 1),
      defaultLimit: await this.getSetting('pagination', 'default_limit', 20),
      maxLimit: await this.getSetting('pagination', 'max_limit', 100),
    };
  }

  async getSecuritySettings() {
    return {
      passwordMinLength: await this.getSetting('security', 'password_min_length', 8),
    };
  }

  async getMiddlewareSettings() {
    return {
      performanceMonitoringThreshold: await this.getSetting('middleware', 'performance_monitoring_threshold', 2000),
      requestSizeMonitoringLimit: await this.getSetting('middleware', 'request_size_monitoring_limit', 5 * 1024 * 1024),
      requestSizeLimit: await this.getSetting('middleware', 'request_size_limit', 10 * 1024 * 1024),
      memoMaxLength: await this.getSetting('middleware', 'memo_max_length', 500),
    };
  }
}

// Test dynamic validation functions
const createDynamicAmountValidation = async (settingsService: TestSettingsService, field: string = 'amount'): Promise<ValidationChain> => {
  try {
    const businessRuleSettings = await settingsService.getBusinessRuleSettings();
    const minAmount = parseFloat(businessRuleSettings.minOrderAmount);
    const maxAmount = parseFloat(businessRuleSettings.maxOrderAmount);
    
    return body(field)
      .isFloat({ min: minAmount, max: maxAmount })
      .withMessage(`Amount must be between ${minAmount} and ${maxAmount}`);
  } catch (error) {
    console.error('Failed to create dynamic amount validation, using fallback:', error);
    return body(field)
      .isFloat({ min: 0.01, max: 1000000 })
      .withMessage('Amount must be between 0.01 and 1000000');
  }
};

const createDynamicPaginationValidation = async (settingsService: TestSettingsService): Promise<ValidationChain[]> => {
  try {
    const paginationSettings = await settingsService.getPaginationSettings();
    const maxLimit = paginationSettings.maxLimit;
    
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: maxLimit })
        .withMessage(`Limit must be between 1 and ${maxLimit}`),
    ];
  } catch (error) {
    console.error('Failed to create dynamic pagination validation, using fallback:', error);
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    ];
  }
};

const createDynamicPasswordValidation = async (settingsService: TestSettingsService): Promise<ValidationChain> => {
  try {
    const securitySettings = await settingsService.getSecuritySettings();
    const minLength = securitySettings.passwordMinLength;
    
    return body('password')
      .isLength({ min: minLength, max: 128 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage(`Password must be ${minLength}-128 characters with uppercase, lowercase, number, and special character`);
  } catch (error) {
    console.error('Failed to create dynamic password validation, using fallback:', error);
    return body('password')
      .isLength({ min: 8, max: 128 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character');
  }
};

const testDynamicValidation = async () => {
  console.log('🧪 Testing Dynamic Validation System...\n');

  try {
    // Initialize database connection
    await initTestDb();

    const settingsService = new TestSettingsService();

    // Test 1: Dynamic amount validation
    console.log('📋 Test 1: Dynamic amount validation');
    
    const amountValidation = await createDynamicAmountValidation(settingsService);
    console.log('    ✅ Dynamic amount validation created successfully');
    
    // Check validation chain properties
    console.log(`    Validation chain type: ${typeof amountValidation}`);
    console.log(`    Has builder: ${typeof amountValidation.builder === 'object'}`);

    // Test 2: Dynamic pagination validation
    console.log('\n📋 Test 2: Dynamic pagination validation');
    
    const paginationValidation = await createDynamicPaginationValidation(settingsService);
    console.log('    ✅ Dynamic pagination validation created successfully');
    console.log(`    Validation chains count: ${paginationValidation.length}`);
    console.log(`    Page validation exists: ${paginationValidation[0] !== undefined}`);
    console.log(`    Limit validation exists: ${paginationValidation[1] !== undefined}`);

    // Test 3: Dynamic password validation
    console.log('\n📋 Test 3: Dynamic password validation');
    
    const passwordValidation = await createDynamicPasswordValidation(settingsService);
    console.log('    ✅ Dynamic password validation created successfully');

    // Test 4: Settings retrieval for validation
    console.log('\n📋 Test 4: Settings retrieval for validation');
    
    const businessRules = await settingsService.getBusinessRuleSettings();
    console.log(`    Min order amount: ${businessRules.minOrderAmount} (${typeof businessRules.minOrderAmount})`);
    console.log(`    Max order amount: ${businessRules.maxOrderAmount} (${typeof businessRules.maxOrderAmount})`);
    
    const paginationSettings = await settingsService.getPaginationSettings();
    console.log(`    Max pagination limit: ${paginationSettings.maxLimit} (${typeof paginationSettings.maxLimit})`);
    
    const securitySettings = await settingsService.getSecuritySettings();
    console.log(`    Password min length: ${securitySettings.passwordMinLength} (${typeof securitySettings.passwordMinLength})`);

    // Test 5: Middleware settings
    console.log('\n📋 Test 5: Middleware settings retrieval');
    
    const middlewareSettings = await settingsService.getMiddlewareSettings();
    console.log(`    Performance threshold: ${middlewareSettings.performanceMonitoringThreshold}ms`);
    console.log(`    Request size limit: ${middlewareSettings.requestSizeLimit} bytes`);
    console.log(`    Memo max length: ${middlewareSettings.memoMaxLength} characters`);
    console.log('    ✅ Middleware settings retrieved successfully');

    // Test 6: Validation with fallback (simulate database error)
    console.log('\n📋 Test 6: Validation fallback mechanism');

    // Close database connection to simulate error
    await testDb.end();

    // Reinitialize with a new connection that we'll immediately close
    testDb = new Pool(DB_CONFIG);
    await testDb.end();

    const fallbackAmountValidation = await createDynamicAmountValidation(settingsService);
    console.log('    ✅ Fallback amount validation created successfully');

    const fallbackPaginationValidation = await createDynamicPaginationValidation(settingsService);
    console.log('    ✅ Fallback pagination validation created successfully');

    const fallbackPasswordValidation = await createDynamicPasswordValidation(settingsService);
    console.log('    ✅ Fallback password validation created successfully');

    console.log('\n🎉 Dynamic validation tests completed!');
    console.log('\n📊 Test Summary:');
    console.log('  ✅ Dynamic amount validation works correctly');
    console.log('  ✅ Dynamic pagination validation works correctly');
    console.log('  ✅ Dynamic password validation works correctly');
    console.log('  ✅ Settings retrieval for validation works');
    console.log('  ✅ Middleware settings retrieval works');
    console.log('  ✅ Fallback mechanisms work when database fails');
    console.log('  ✅ All validation chains are properly constructed');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Ensure database connection is closed
    if (testDb && !testDb.ended) {
      await testDb.end();
      console.log('✅ Database connection closed');
    }
  }
};

// Run the test
if (require.main === module) {
  testDynamicValidation().then(() => {
    console.log('\n✅ Dynamic validation test script completed');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Dynamic validation test script failed:', error);
    process.exit(1);
  });
}

export { testDynamicValidation };
