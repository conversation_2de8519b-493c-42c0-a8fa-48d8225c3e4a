'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { adminApi } from '@/lib/api';
import {
  Users,
  ShoppingCart,
  Gift,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  Activity,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface AdminStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: string;
  activeVouchers: number;
  pendingOrders: number;
  recentActivity: ActivityItem[];
}

interface ActivityItem {
  id: string;
  type: 'order' | 'user' | 'voucher' | 'payment';
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error';
}

export default function AdminDashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalOrders: 0,
    totalRevenue: '0.00',
    activeVouchers: 0,
    pendingOrders: 0,
    recentActivity: [],
  });
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'admin')) {
      router.push('/dashboard');
      return;
    }
    
    if (user?.role === 'admin') {
      loadAdminStats();
    }
  }, [user, loading, router]);

  const loadAdminStats = async () => {
    try {
      // Load admin statistics from real API
      const data = await adminApi.getStats();

      if (data.success) {
        // Transform recent orders and users into activity items
        const recentActivity: ActivityItem[] = [];

        // Add recent orders to activity
        if (data.data.recentOrders) {
          data.data.recentOrders.forEach((order: any) => {
            recentActivity.push({
              id: order.id,
              type: 'order',
              description: `Order ${order.id} - ${order.status} (${order.amount} ${order.currency})`,
              timestamp: order.created_at,
              status: order.status === 'completed' ? 'success' :
                      order.status === 'pending' ? 'warning' : 'error',
            });
          });
        }

        // Add recent users to activity
        if (data.data.recentUsers) {
          data.data.recentUsers.forEach((user: any) => {
            recentActivity.push({
              id: user.id,
              type: 'user',
              description: `New user registration: ${user.email}`,
              timestamp: user.created_at,
              status: user.email_verified ? 'success' : 'warning',
            });
          });
        }

        // Sort activity by timestamp (most recent first)
        recentActivity.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        setStats({
          totalUsers: data.data.stats.totalUsers,
          totalOrders: data.data.stats.totalOrders,
          totalRevenue: data.data.stats.totalRevenue.toFixed(2),
          activeVouchers: data.data.stats.activeVouchers,
          pendingOrders: data.data.stats.totalOrders - data.data.stats.completedOrders,
          recentActivity: recentActivity.slice(0, 5), // Show only 5 most recent
        });
      }
    } catch (error) {
      console.error('Failed to load admin stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-4 w-4" />;
      case 'user':
        return <Users className="h-4 w-4" />;
      case 'voucher':
        return <Gift className="h-4 w-4" />;
      case 'payment':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-success-600';
      case 'warning':
        return 'text-warning-600';
      case 'error':
        return 'text-error-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading || statsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner-lg mx-auto mb-4" />
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Monitor platform performance and manage operations</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ShoppingCart className="h-8 w-8 text-ton-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalOrders.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalRevenue} TON</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Gift className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Vouchers</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeVouchers.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Pending Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingOrders}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
                  <Link
                    href="/admin/activity"
                    className="text-sm text-ton-600 hover:text-ton-500"
                  >
                    View all
                  </Link>
                </div>
              </div>
              <div className="card-body p-0">
                <div className="divide-y divide-gray-200">
                  {stats.recentActivity.map((activity) => (
                    <div key={activity.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-full bg-gray-100 ${getStatusColor(activity.status)}`}>
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900">{activity.description}</p>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatDate(activity.timestamp)}
                          </p>
                        </div>
                        <div className={`flex-shrink-0 ${getStatusColor(activity.status)}`}>
                          {activity.status === 'success' && <CheckCircle className="h-4 w-4" />}
                          {activity.status === 'warning' && <Clock className="h-4 w-4" />}
                          {activity.status === 'error' && <AlertCircle className="h-4 w-4" />}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
              </div>
              <div className="card-body space-y-3">
                <Link
                  href="/admin/users"
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">Manage Users</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </Link>

                <Link
                  href="/admin/orders"
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <ShoppingCart className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">View Orders</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </Link>

                <Link
                  href="/admin/vouchers"
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Gift className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">Manage Vouchers</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </Link>

                <Link
                  href="/admin/analytics"
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <BarChart3 className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">Analytics</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </Link>

                <Link
                  href="/admin/settings"
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Activity className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">System Settings</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </Link>
              </div>
            </div>

            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">System Status</h2>
              </div>
              <div className="card-body space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">API Status</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-success-600 rounded-full"></div>
                    <span className="text-sm text-success-600">Operational</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Database</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-success-600 rounded-full"></div>
                    <span className="text-sm text-success-600">Connected</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">TON Network</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-success-600 rounded-full"></div>
                    <span className="text-sm text-success-600">Online</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Payment Processing</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-warning-600 rounded-full"></div>
                    <span className="text-sm text-warning-600">Delayed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
