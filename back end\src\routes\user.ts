import { Router } from 'express';
import {
  getProfile,
  updateProfile,
  changePassword,
  getUserOrders,
  getUserVouchers,
  deleteAccount,
} from '../controllers/userController';
import {
  validateInput,
  memoValidation,
} from '../middleware/security';
import { authenticate, requireUser } from '../middleware/auth';
import { body, query } from 'express-validator';

const router = Router();

// All user routes require authentication
router.use(authenticate);
router.use(requireUser);

// Profile validation
const updateProfileValidation = [
  body('telegramId')
    .optional()
    .matches(/^[a-zA-Z0-9_]{5,32}$/)
    .withMessage('Telegram ID must be 5-32 characters, alphanumeric and underscores only'),
  memoValidation,
];

// Change password validation
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8, max: 128 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must be 8-128 characters with uppercase, lowercase, number, and special character'),
];

// Delete account validation
const deleteAccountValidation = [
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  body('confirmDelete')
    .equals('DELETE')
    .withMessage('Please confirm deletion by typing "DELETE"'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Voucher status validation
const voucherStatusValidation = [
  query('status')
    .optional()
    .isIn(['active', 'redeemed', 'expired', 'cancelled'])
    .withMessage('Invalid voucher status'),
];

/**
 * @route   GET /api/v1/user/profile
 * @desc    Get user profile
 * @access  Private
 */
router.get('/profile', getProfile);

/**
 * @route   PUT /api/v1/user/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put(
  '/profile',
  validateInput(updateProfileValidation),
  updateProfile
);

/**
 * @route   POST /api/v1/user/change-password
 * @desc    Change user password
 * @access  Private
 */
router.post(
  '/change-password',
  validateInput(changePasswordValidation),
  changePassword
);

/**
 * @route   GET /api/v1/user/orders
 * @desc    Get user orders with pagination
 * @access  Private
 */
router.get(
  '/orders',
  validateInput(paginationValidation),
  getUserOrders
);

/**
 * @route   GET /api/v1/user/vouchers
 * @desc    Get user vouchers with pagination and filtering
 * @access  Private
 */
router.get(
  '/vouchers',
  validateInput([...paginationValidation, ...voucherStatusValidation]),
  getUserVouchers
);

/**
 * @route   DELETE /api/v1/user/account
 * @desc    Delete user account (soft delete)
 * @access  Private
 */
router.delete(
  '/account',
  validateInput(deleteAccountValidation),
  deleteAccount
);

export default router;
