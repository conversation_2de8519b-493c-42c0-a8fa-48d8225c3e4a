'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Mail, RefreshCw, CheckCircle, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function ResendVerificationPage() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  
  const router = useRouter();
  const { resendVerification } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setStatus('error');
      setMessage('Please enter your email address');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setStatus('error');
      setMessage('Please enter a valid email address');
      return;
    }

    setStatus('loading');
    setMessage('');

    try {
      const success = await resendVerification(email);
      if (success) {
        setStatus('success');
        setMessage('A new verification email has been sent to your inbox. Please check your email and follow the instructions to verify your account.');
      } else {
        setStatus('error');
        setMessage('Failed to send verification email. Please try again.');
      }
    } catch (error: any) {
      setStatus('error');
      const errorMessage = error.response?.data?.error || 'Failed to send verification email. Please try again.';
      setMessage(errorMessage);
    }
  };

  const renderContent = () => {
    if (status === 'success') {
      return (
        <div className="text-center">
          <CheckCircle className="h-16 w-16 text-success-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Verification Email Sent!
          </h2>
          <p className="text-gray-600 mb-6">
            {message}
          </p>
          <div className="space-y-3">
            <Link
              href="/auth/login"
              className="btn-primary w-full"
            >
              Continue to Login
            </Link>
            <Link
              href="/auth/verify-email"
              className="btn-outline w-full"
            >
              Already have a token? Verify Email
            </Link>
          </div>
        </div>
      );
    }

    return (
      <div>
        <div className="text-center mb-6">
          <Mail className="h-16 w-16 text-ton-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Resend Verification Email
          </h2>
          <p className="text-gray-600">
            Enter your email address and we'll send you a new verification link.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="form-input pl-10"
                placeholder="Enter your email address"
                required
                disabled={status === 'loading'}
              />
            </div>
          </div>

          {status === 'error' && message && (
            <div className="alert-error">
              <p className="text-sm">{message}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={status === 'loading'}
            className="btn-primary w-full"
          >
            {status === 'loading' ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Send Verification Email
              </>
            )}
          </button>
        </form>

        <div className="mt-6 space-y-3">
          <Link
            href="/auth/login"
            className="btn-ghost w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Login
          </Link>
          <Link
            href="/auth/register"
            className="btn-outline w-full"
          >
            Create New Account
          </Link>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gradient">TON Vouchers</h1>
        </div>

        {/* Content */}
        <div className="card">
          <div className="card-body">
            {renderContent()}
          </div>
        </div>

        {/* Help Text */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Need help?{' '}
            <Link
              href="/support"
              className="font-medium text-ton-600 hover:text-ton-500"
            >
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
