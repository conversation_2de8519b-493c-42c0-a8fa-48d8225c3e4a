// User Types
export interface User {
  id: string;
  email: string;
  telegramId: string;
  emailVerified: boolean;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export type UserRole = 'user' | 'admin';

export interface UserRegistration {
  email: string;
  telegramId: string;
  password: string;
  memo?: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  status: OrderStatus;
  amount: string;
  currency: string;
  memo?: string;
  paymentAddress?: string;
  transactionHash?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type OrderStatus = 'pending' | 'payment_pending' | 'paid' | 'completed' | 'cancelled' | 'failed';

export interface CreateOrderRequest {
  amount: string;
  currency: string;
  memo?: string;
}

// Voucher Types
export interface Voucher {
  id: string;
  orderId: string;
  code: string;
  status: VoucherStatus;
  redeemedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
}

export type VoucherStatus = 'active' | 'redeemed' | 'expired' | 'cancelled';

export interface VoucherRedemption {
  code: string;
  userId?: string;
}

// Transaction Types
export interface Transaction {
  id: string;
  orderId: string;
  hash: string;
  fromAddress: string;
  toAddress: string;
  amount: string;
  status: TransactionStatus;
  confirmations: number;
  createdAt: Date;
}

export type TransactionStatus = 'pending' | 'confirmed' | 'failed' | 'cancelled';

// Payment Types
export interface PaymentRequest {
  orderId: string;
  amount: string;
  currency: string;
  paymentAddress: string;
  expiresAt: Date;
}

export interface PaymentVerification {
  transactionHash: string;
  amount: string;
  fromAddress: string;
  toAddress: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Authentication Types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Validation Types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Email Types
export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface VoucherDeliveryEmail {
  userEmail: string;
  userName: string;
  voucherCode: string;
  orderDetails: {
    orderId: string;
    amount: string;
    currency: string;
    createdAt: Date;
  };
}

// Admin Types
export interface AdminStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: string;
  activeVouchers: number;
  redeemedVouchers: number;
}

export interface AdminUser extends User {
  lastLogin?: Date;
  permissions: string[];
}

// TON Integration Types
export interface TONWalletConnection {
  address: string;
  publicKey: string;
  walletType: string;
  connected: boolean;
}

export interface TONTransaction {
  hash: string;
  from: string;
  to: string;
  amount: string;
  fee: string;
  timestamp: number;
  confirmations: number;
  memo?: string;
}

// Error Types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Utility Types
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterOptions {
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  userId?: string;
}

// Constants
export const USER_ROLES = {
  USER: 'user' as const,
  ADMIN: 'admin' as const,
};

export const ORDER_STATUSES = {
  PENDING: 'pending' as const,
  PAYMENT_PENDING: 'payment_pending' as const,
  PAID: 'paid' as const,
  COMPLETED: 'completed' as const,
  CANCELLED: 'cancelled' as const,
  FAILED: 'failed' as const,
};

export const VOUCHER_STATUSES = {
  ACTIVE: 'active' as const,
  REDEEMED: 'redeemed' as const,
  EXPIRED: 'expired' as const,
  CANCELLED: 'cancelled' as const,
};

export const TRANSACTION_STATUSES = {
  PENDING: 'pending' as const,
  CONFIRMED: 'confirmed' as const,
  FAILED: 'failed' as const,
  CANCELLED: 'cancelled' as const,
};
