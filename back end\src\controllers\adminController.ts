import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logAdminAction } from '../config/logger';
import { generateVoucherCode, generatePaginationMeta } from '../utils/helpers';
import { settingsService } from '../services/settingsService';

// Get admin dashboard statistics
export const getAdminStats = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;

    const statsResult = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE role IS NOT NULL) as total_users,
        (SELECT COUNT(*) FROM users WHERE email_verified = true) as verified_users,
        (SELECT COUNT(*) FROM orders) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'completed') as completed_orders,
        (SELECT COUNT(*) FROM vouchers) as total_vouchers,
        (SELECT COUNT(*) FROM vouchers WHERE status = 'active') as active_vouchers,
        (SELECT COALESCE(SUM(amount), 0) FROM orders WHERE status = 'completed') as total_revenue,
        (SELECT COUNT(*) FROM orders WHERE created_at >= CURRENT_DATE) as orders_today,
        (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE) as users_today
    `);

    const stats = statsResult.rows[0];

    // Get recent activity
    const recentOrdersResult = await executeQuery(`
      SELECT o.id, o.amount, o.currency, o.status, o.created_at, u.telegram_id
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `);

    const recentUsersResult = await executeQuery(`
      SELECT id, email, telegram_id, email_verified, role, created_at
      FROM users
      WHERE role IS NOT NULL
      ORDER BY created_at DESC
      LIMIT 10
    `);

    logAdminAction('VIEW_DASHBOARD', adminId, 'dashboard', 'stats', {}, req);

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers: parseInt(stats.total_users),
          verifiedUsers: parseInt(stats.verified_users),
          totalOrders: parseInt(stats.total_orders),
          completedOrders: parseInt(stats.completed_orders),
          totalVouchers: parseInt(stats.total_vouchers),
          activeVouchers: parseInt(stats.active_vouchers),
          totalRevenue: parseFloat(stats.total_revenue),
          ordersToday: parseInt(stats.orders_today),
          usersToday: parseInt(stats.users_today),
        },
        recentOrders: recentOrdersResult.rows,
        recentUsers: recentUsersResult.rows,
      },
    });
  } catch (error) {
    logger.error('Get admin stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get admin statistics',
    });
  }
};

// Get all users with pagination and filtering
export const getUsers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const search = req.query.search as string;
    const role = req.query.role as string;
    const verified = req.query.verified as string;

    // Build query conditions
    let whereClause = 'WHERE role IS NOT NULL';
    const queryParams: any[] = [];

    if (search) {
      whereClause += ' AND (email ILIKE $1 OR telegram_id ILIKE $1)';
      queryParams.push(`%${search}%`);
    }

    if (role) {
      whereClause += ` AND role = $${queryParams.length + 1}`;
      queryParams.push(role);
    }

    if (verified) {
      whereClause += ` AND email_verified = $${queryParams.length + 1}`;
      queryParams.push(verified === 'true');
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM users ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get users
    const usersQuery = `
      SELECT id, email, telegram_id, email_verified, role, 
             failed_login_attempts, locked_until, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const usersResult = await executeQuery(usersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_USERS', adminId, 'users', 'list', { search, role, verified }, req);

    res.json({
      success: true,
      data: {
        users: usersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get users',
    });
  }
};

// Update user role or status
export const updateUser = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { id } = req.params;
    const { role, emailVerified, locked } = req.body;

    // Get current user data
    const userResult = await executeQuery(
      'SELECT id, email, role, email_verified, locked_until FROM users WHERE id = $1',
      [id]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Prevent admin from changing their own role
    if (user.id === adminId && role && role !== user.role) {
      return res.status(400).json({
        success: false,
        error: 'Cannot change your own role',
      });
    }

    // Build update query
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (role !== undefined) {
      updates.push(`role = $${paramIndex++}`);
      params.push(role);
    }

    if (emailVerified !== undefined) {
      updates.push(`email_verified = $${paramIndex++}`);
      params.push(emailVerified);
    }

    if (locked !== undefined) {
      if (locked) {
        // Get lockout duration from security settings
        const securitySettings = await settingsService.getSecuritySettings();
        const lockoutDuration = securitySettings.lockoutDurationMinutes * 60 * 1000; // Convert minutes to milliseconds
        updates.push(`locked_until = $${paramIndex++}`);
        params.push(new Date(Date.now() + lockoutDuration));
      } else {
        updates.push(`locked_until = NULL, failed_login_attempts = 0`);
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid updates provided',
      });
    }

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(id);

    const updateQuery = `
      UPDATE users 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, email, telegram_id, email_verified, role, locked_until, updated_at
    `;

    const updateResult = await executeQuery(updateQuery, params);
    const updatedUser = updateResult.rows[0];

    logAdminAction('UPDATE_USER', adminId, 'user', id, { 
      changes: { role, emailVerified, locked },
      userEmail: user.email,
    }, req);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: updatedUser,
      },
    });
  } catch (error) {
    logger.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user',
    });
  }
};

// Get all orders with pagination and filtering
export const getOrders = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;
    const userId = req.query.userId as string;

    // Build query conditions
    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];

    if (status) {
      whereClause += ` AND o.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    if (userId) {
      whereClause += ` AND o.user_id = $${queryParams.length + 1}`;
      queryParams.push(userId);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM orders o ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get orders
    const ordersQuery = `
      SELECT o.id, o.user_id, o.status, o.amount, o.currency, o.memo,
             o.payment_address, o.transaction_hash, o.created_at, o.updated_at,
             u.email, u.telegram_id
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${whereClause}
      ORDER BY o.created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const ordersResult = await executeQuery(ordersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_ORDERS', adminId, 'orders', 'list', { status, userId }, req);

    res.json({
      success: true,
      data: {
        orders: ordersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get orders',
    });
  }
};

// Get all vouchers with pagination and filtering
export const getVouchers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;
    const userId = req.query.userId as string;

    // Build query conditions
    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];

    if (status) {
      whereClause += ` AND v.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    if (userId) {
      whereClause += ` AND o.user_id = $${queryParams.length + 1}`;
      queryParams.push(userId);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) 
      FROM vouchers v 
      JOIN orders o ON v.order_id = o.id 
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get vouchers
    const vouchersQuery = `
      SELECT v.id, v.code, v.status, v.redeemed_at, v.expires_at, v.created_at,
             o.id as order_id, o.user_id, o.amount, o.currency,
             u.email, u.telegram_id
      FROM vouchers v
      JOIN orders o ON v.order_id = o.id
      JOIN users u ON o.user_id = u.id
      ${whereClause}
      ORDER BY v.created_at DESC 
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;
    queryParams.push(limit, offset);

    const vouchersResult = await executeQuery(vouchersQuery, queryParams);

    const pagination = generatePaginationMeta(total, page, limit);

    logAdminAction('VIEW_VOUCHERS', adminId, 'vouchers', 'list', { status, userId }, req);

    res.json({
      success: true,
      data: {
        vouchers: vouchersResult.rows,
        pagination,
      },
    });
  } catch (error) {
    logger.error('Get vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vouchers',
    });
  }
};

// Bulk create vouchers
export const bulkCreateVouchers = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { count, expiryDays = 365 } = req.body;

    if (!count || count < 1 || count > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Count must be between 1 and 1000',
      });
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiryDays);

    const vouchers = [];
    const queries = [];

    // Generate unique voucher codes
    for (let i = 0; i < count; i++) {
      let voucherCode: string;
      let isUnique = false;
      let attempts = 0;

      do {
        voucherCode = generateVoucherCode();
        const codeCheck = await executeQuery(
          'SELECT id FROM vouchers WHERE code = $1',
          [voucherCode]
        );
        isUnique = codeCheck.rows.length === 0;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        return res.status(500).json({
          success: false,
          error: 'Failed to generate unique voucher codes',
        });
      }

      const voucherId = uuidv4();
      vouchers.push({
        id: voucherId,
        code: voucherCode,
        expiresAt,
      });

      queries.push({
        text: `INSERT INTO vouchers (id, code, status, expires_at) VALUES ($1, $2, $3, $4)`,
        params: [voucherId, voucherCode, 'active', expiresAt],
      });
    }

    // Execute all inserts in a transaction
    await executeTransaction(queries);

    logAdminAction('BULK_CREATE_VOUCHERS', adminId, 'vouchers', 'bulk', { 
      count, 
      expiryDays,
      voucherIds: vouchers.map(v => v.id),
    }, req);

    res.status(201).json({
      success: true,
      message: `${count} vouchers created successfully`,
      data: {
        vouchers: vouchers.map(v => ({
          id: v.id,
          code: v.code,
          status: 'active',
          expiresAt: v.expiresAt,
        })),
      },
    });
  } catch (error) {
    logger.error('Bulk create vouchers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create vouchers',
    });
  }
};

// Create individual voucher
export const createVoucher = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const {
      productId,
      name,
      description,
      amount,
      currency = 'TON',
      expiryDays = 365,
      quantity = 1,
      available = true
    } = req.body;

    if (!productId || !name || !description || !amount) {
      return res.status(400).json({
        success: false,
        error: 'Product ID, name, description, and amount are required',
      });
    }

    if (quantity < 1 || quantity > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Quantity must be between 1 and 1000',
      });
    }

    // Verify product exists
    const productResult = await executeQuery(
      'SELECT id, name FROM products WHERE id = $1',
      [productId]
    );

    if (productResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Product not found',
      });
    }

    const product = productResult.rows[0];
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiryDays);

    const vouchers: any[] = [];
    const queries: any[] = [];

    // Create vouchers
    for (let i = 0; i < quantity; i++) {
      const voucherId = uuidv4();
      const orderId = uuidv4();
      let voucherCode: string;
      let isUnique = false;
      let attempts = 0;

      // Generate unique voucher code
      do {
        voucherCode = generateVoucherCode();
        const codeCheck = await executeQuery(
          'SELECT id FROM vouchers WHERE code = $1',
          [voucherCode]
        );
        isUnique = codeCheck.rows.length === 0;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        throw new Error('Failed to generate unique voucher code');
      }

      // Create order for voucher
      queries.push({
        text: `INSERT INTO orders (id, user_id, status, amount, currency, memo, product_id, quantity, payment_address)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        params: [
          orderId,
          adminId, // Admin creates the order
          'completed', // Mark as completed since it's admin-created
          amount,
          currency,
          `Admin created voucher: ${name}`,
          productId,
          1,
          null // No payment address needed for admin-created vouchers
        ]
      });

      // Create voucher
      queries.push({
        text: `INSERT INTO vouchers (id, order_id, code, status, expires_at)
                VALUES ($1, $2, $3, $4, $5)`,
        params: [
          voucherId,
          orderId,
          voucherCode,
          available ? 'active' : 'cancelled',
          expiresAt
        ]
      });

      vouchers.push({
        id: voucherId,
        code: voucherCode,
        orderId,
        status: available ? 'active' : 'cancelled',
        expiresAt,
      });
    }

    // Execute all inserts in a transaction
    await executeTransaction(queries);

    logAdminAction('CREATE_VOUCHER', adminId, 'vouchers', 'create', {
      productId,
      name,
      description,
      amount,
      currency,
      quantity,
      expiryDays,
      voucherIds: vouchers.map(v => v.id),
    }, req);

    res.status(201).json({
      success: true,
      message: `${quantity} voucher${quantity > 1 ? 's' : ''} created successfully`,
      data: {
        vouchers: vouchers.map(v => ({
          id: v.id,
          code: v.code,
          status: v.status,
          expiresAt: v.expiresAt,
        })),
      },
    });
  } catch (error) {
    logger.error('Create voucher error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create voucher',
    });
  }
};

// Update voucher status
export const updateVoucherStatus = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { voucherId } = req.params;
    const { status } = req.body;

    if (!['active', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Status must be either "active" or "cancelled"',
      });
    }

    // Check if voucher exists and is not redeemed
    const voucherResult = await executeQuery(
      'SELECT id, code, status FROM vouchers WHERE id = $1',
      [voucherId]
    );

    if (voucherResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found',
      });
    }

    const voucher = voucherResult.rows[0];

    if (voucher.status === 'redeemed') {
      return res.status(400).json({
        success: false,
        error: 'Cannot change status of redeemed voucher',
      });
    }

    // Update voucher status
    await executeQuery(
      'UPDATE vouchers SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [status, voucherId]
    );

    logAdminAction('UPDATE_VOUCHER_STATUS', adminId, 'vouchers', voucherId, {
      oldStatus: voucher.status,
      newStatus: status,
      voucherCode: voucher.code,
    }, req);

    res.json({
      success: true,
      message: `Voucher ${status === 'active' ? 'enabled' : 'disabled'} successfully`,
    });
  } catch (error) {
    logger.error('Update voucher status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voucher status',
    });
  }
};

// PRODUCT MANAGEMENT

// Get all products with pagination and filtering
export const getProducts = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const {
      page = 1,
      limit = 20,
      category = '',
      available = '',
      search = ''
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    let whereConditions = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (category) {
      whereConditions.push(`p.category = $${paramIndex}`);
      queryParams.push(category);
      paramIndex++;
    }

    if (available !== '') {
      whereConditions.push(`p.available = $${paramIndex}`);
      queryParams.push(available === 'true');
      paramIndex++;
    }

    if (search) {
      whereConditions.push(`(p.name ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) as total FROM products p ${whereClause}`,
      queryParams
    );
    const total = parseInt(countResult.rows[0].total);

    // Get products with pagination
    const productsResult = await executeQuery(
      `SELECT p.*, pc.name as category_name, pc.description as category_description
       FROM products p
       LEFT JOIN product_categories pc ON p.category = pc.id
       ${whereClause}
       ORDER BY p.sort_order ASC, p.created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...queryParams, Number(limit), offset]
    );

    logAdminAction('VIEW_PRODUCTS', adminId, 'products', 'list', {
      page,
      limit,
      category,
      available,
      search,
      total
    }, req);

    res.json({
      success: true,
      data: {
        products: productsResult.rows,
        pagination: generatePaginationMeta(Number(page), Number(limit), total),
      },
    });
  } catch (error) {
    logger.error('Get products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get products',
    });
  }
};

// Get product by ID
export const getProductById = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    const productResult = await executeQuery(
      `SELECT p.*, pc.name as category_name, pc.description as category_description
       FROM products p
       LEFT JOIN product_categories pc ON p.category = pc.id
       WHERE p.id = $1`,
      [productId]
    );

    if (productResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
      });
    }

    res.json({
      success: true,
      data: { product: productResult.rows[0] },
    });
  } catch (error) {
    logger.error('Get product by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get product',
    });
  }
};

// Create new product
export const createProduct = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const {
      id,
      name,
      description,
      category,
      minAmount,
      maxAmount,
      currency = 'TON',
      imageUrl,
      popular = false,
      available = true,
      features = [],
      metadata = {},
      sortOrder = 0,
    } = req.body;

    // Check if product ID already exists
    const existingProduct = await executeQuery(
      'SELECT id FROM products WHERE id = $1',
      [id]
    );

    if (existingProduct.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Product ID already exists',
      });
    }

    // Verify category exists
    const categoryResult = await executeQuery(
      'SELECT id FROM product_categories WHERE id = $1',
      [category]
    );

    if (categoryResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Category not found',
      });
    }

    // Create product
    const productResult = await executeQuery(
      `INSERT INTO products (
        id, name, description, category, min_amount, max_amount, currency,
        image_url, popular, available, features, metadata, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *`,
      [
        id, name, description, category, minAmount, maxAmount, currency,
        imageUrl, popular, available, JSON.stringify(features), JSON.stringify(metadata), sortOrder
      ]
    );

    logAdminAction('CREATE_PRODUCT', adminId, 'products', id, {
      name,
      category,
      minAmount,
      maxAmount,
      currency,
    }, req);

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: { product: productResult.rows[0] },
    });
  } catch (error) {
    logger.error('Create product error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create product',
    });
  }
};

// Update product
export const updateProduct = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { productId } = req.params;
    const {
      name,
      description,
      category,
      minAmount,
      maxAmount,
      currency,
      imageUrl,
      popular,
      available,
      features,
      metadata,
      sortOrder,
    } = req.body;

    // Check if product exists
    const existingProduct = await executeQuery(
      'SELECT id FROM products WHERE id = $1',
      [productId]
    );

    if (existingProduct.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
      });
    }

    // Verify category exists if provided
    if (category) {
      const categoryResult = await executeQuery(
        'SELECT id FROM product_categories WHERE id = $1',
        [category]
      );

      if (categoryResult.rows.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Category not found',
        });
      }
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex}`);
      updateValues.push(name);
      paramIndex++;
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      updateValues.push(description);
      paramIndex++;
    }
    if (category !== undefined) {
      updateFields.push(`category = $${paramIndex}`);
      updateValues.push(category);
      paramIndex++;
    }
    if (minAmount !== undefined) {
      updateFields.push(`min_amount = $${paramIndex}`);
      updateValues.push(minAmount);
      paramIndex++;
    }
    if (maxAmount !== undefined) {
      updateFields.push(`max_amount = $${paramIndex}`);
      updateValues.push(maxAmount);
      paramIndex++;
    }
    if (currency !== undefined) {
      updateFields.push(`currency = $${paramIndex}`);
      updateValues.push(currency);
      paramIndex++;
    }
    if (imageUrl !== undefined) {
      updateFields.push(`image_url = $${paramIndex}`);
      updateValues.push(imageUrl);
      paramIndex++;
    }
    if (popular !== undefined) {
      updateFields.push(`popular = $${paramIndex}`);
      updateValues.push(popular);
      paramIndex++;
    }
    if (available !== undefined) {
      updateFields.push(`available = $${paramIndex}`);
      updateValues.push(available);
      paramIndex++;
    }
    if (features !== undefined) {
      updateFields.push(`features = $${paramIndex}`);
      updateValues.push(JSON.stringify(features));
      paramIndex++;
    }
    if (metadata !== undefined) {
      updateFields.push(`metadata = $${paramIndex}`);
      updateValues.push(JSON.stringify(metadata));
      paramIndex++;
    }
    if (sortOrder !== undefined) {
      updateFields.push(`sort_order = $${paramIndex}`);
      updateValues.push(sortOrder);
      paramIndex++;
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(productId);

    const productResult = await executeQuery(
      `UPDATE products SET ${updateFields.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      updateValues
    );

    logAdminAction('UPDATE_PRODUCT', adminId, 'products', productId, req.body, req);

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: { product: productResult.rows[0] },
    });
  } catch (error) {
    logger.error('Update product error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update product',
    });
  }
};

// Delete product
export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { productId } = req.params;

    // Check if product exists
    const existingProduct = await executeQuery(
      'SELECT id, name FROM products WHERE id = $1',
      [productId]
    );

    if (existingProduct.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
      });
    }

    // Check if product is used in any orders
    const ordersResult = await executeQuery(
      'SELECT COUNT(*) as count FROM orders WHERE product_id = $1',
      [productId]
    );

    if (parseInt(ordersResult.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete product that has been ordered. Consider marking it as unavailable instead.',
      });
    }

    // Delete product
    await executeQuery('DELETE FROM products WHERE id = $1', [productId]);

    logAdminAction('DELETE_PRODUCT', adminId, 'products', productId, {
      productName: existingProduct.rows[0].name,
    }, req);

    res.json({
      success: true,
      message: 'Product deleted successfully',
    });
  } catch (error) {
    logger.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete product',
    });
  }
};

// Get all product categories
export const getProductCategories = async (req: Request, res: Response) => {
  try {
    const categoriesResult = await executeQuery(
      'SELECT * FROM product_categories ORDER BY sort_order ASC, name ASC'
    );

    res.json({
      success: true,
      data: { categories: categoriesResult.rows },
    });
  } catch (error) {
    logger.error('Get product categories error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get categories',
    });
  }
};

// Create product category
export const createProductCategory = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { id, name, description, icon, sortOrder = 0, active = true } = req.body;

    // Check if category ID already exists
    const existingCategory = await executeQuery(
      'SELECT id FROM product_categories WHERE id = $1',
      [id]
    );

    if (existingCategory.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Category ID already exists',
      });
    }

    const categoryResult = await executeQuery(
      `INSERT INTO product_categories (id, name, description, icon, sort_order, active)
       VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
      [id, name, description, icon, sortOrder, active]
    );

    logAdminAction('CREATE_CATEGORY', adminId, 'product_categories', id, {
      name,
      description,
    }, req);

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: { category: categoryResult.rows[0] },
    });
  } catch (error) {
    logger.error('Create product category error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create category',
    });
  }
};
