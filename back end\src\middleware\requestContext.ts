import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger, logApiRequest } from '../config/logger';

// Extend Express Request interface to include context
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
      correlationId?: string;
    }
  }
}

/**
 * Request context middleware
 * Adds request ID, correlation ID, and timing information to each request
 */
export const requestContext = (req: Request, res: Response, next: NextFunction) => {
  // Generate unique request ID
  req.id = req.headers['x-request-id'] as string || uuidv4();
  
  // Set or generate correlation ID for request tracing
  req.correlationId = req.headers['x-correlation-id'] as string || uuidv4();
  
  // Record request start time for performance tracking
  req.startTime = Date.now();
  
  // Add request ID to response headers for client tracking
  res.setHeader('X-Request-ID', req.id);
  res.setHeader('X-Correlation-ID', req.correlationId);
  
  // Log incoming request
  logger.http('Incoming Request', {
    requestId: req.id,
    correlationId: req.correlationId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString(),
  });
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any): Response {
    const duration = Date.now() - (req.startTime || Date.now());

    // Log API request completion
    logApiRequest(
      req.method,
      req.originalUrl,
      res.statusCode,
      duration,
      req.user?.id
    );

    // Log response details
    logger.http('Request Completed', {
      requestId: req.id,
      correlationId: req.correlationId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      userId: req.user?.id,
      timestamp: new Date().toISOString(),
    });

    // Call original end method and return its result
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

/**
 * Performance monitoring middleware
 * Logs slow requests and performance metrics
 */
export const performanceMonitoring = (slowRequestThreshold: number = 1000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalEnd = res.end;

    res.end = function(chunk?: any, encoding?: any): Response {
      const duration = Date.now() - (req.startTime || Date.now());

      // Log slow requests
      if (duration > slowRequestThreshold) {
        logger.warn('Slow Request Detected', {
          requestId: req.id,
          correlationId: req.correlationId,
          method: req.method,
          url: req.originalUrl,
          duration,
          threshold: slowRequestThreshold,
          userId: req.user?.id,
          timestamp: new Date().toISOString(),
        });
      }

      // Log performance metrics for specific endpoints
      if (req.originalUrl.includes('/api/')) {
        logger.debug('Performance Metric', {
          requestId: req.id,
          endpoint: req.originalUrl,
          method: req.method,
          duration,
          statusCode: res.statusCode,
          timestamp: new Date().toISOString(),
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };
};

/**
 * Error context middleware
 * Adds request context to error objects
 */
export const errorContext = (req: Request, _res: Response, next: NextFunction) => {
  // Store original next function
  const originalNext = next;

  // Override next to add context to errors
  const contextualNext = (error?: any) => {
    if (error) {
      // Add request context to error
      error.requestContext = {
        requestId: req.id,
        correlationId: req.correlationId,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
      };
    }

    originalNext(error);
  };

  // Replace next function in request
  (req as any).next = contextualNext;

  next();
};

/**
 * Request size monitoring
 * Logs large requests for performance analysis
 */
export const requestSizeMonitoring = (largeSizeThreshold: number = 1024 * 1024) => { // 1MB default
  return (req: Request, _res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('content-length') || '0');

    if (contentLength > largeSizeThreshold) {
      logger.warn('Large Request Detected', {
        requestId: req.id,
        correlationId: req.correlationId,
        method: req.method,
        url: req.originalUrl,
        contentLength,
        threshold: largeSizeThreshold,
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
      });
    }

    next();
  };
};

/**
 * User activity logging
 * Logs user actions for audit trails
 */
export const userActivityLogging = (req: Request, res: Response, next: NextFunction) => {
  // Only log for authenticated users and specific methods
  if (req.user && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
    const originalEnd = res.end;

    res.end = function(chunk?: any, encoding?: any): Response {
      // Only log successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        logger.info('User Activity', {
          requestId: req.id,
          correlationId: req.correlationId,
          userId: req.user?.id,
          userEmail: req.user?.email,
          action: `${req.method} ${req.originalUrl}`,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };
  }

  next();
};
