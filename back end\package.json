{"name": "@tonsite/backend", "version": "1.0.0", "description": "Backend API server for TON Voucher Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "node dist/scripts/migrate.js", "seed": "node dist/scripts/seed.js", "setup-admin": "ts-node src/scripts/setup-admin.ts", "setup-admin:prod": "NODE_ENV=production ts-node src/scripts/setup-admin.ts", "setup:users": "ts-node src/scripts/setup-test-users.ts", "test:settings": "ts-node src/scripts/test-settings.ts", "test:db": "ts-node src/scripts/test-db-simple.ts", "clean": "rm -rf dist", "audit": "npm audit"}, "dependencies": {"@ton/core": "^0.56.0", "@ton/crypto": "^3.2.0", "@ton/ton": "^13.9.0", "@types/ws": "^8.18.1", "bcrypt": "^5.1.1", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.11.3", "redis": "^4.6.10", "sharp": "^0.32.6", "tonweb": "^0.0.66", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.18.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/csurf": "^1.11.5", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}