@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-ton-600 text-white hover:bg-ton-700 focus:ring-ton-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent border-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  .btn-disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
  }
  
  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-ton-500 focus:border-ton-500 sm:text-sm;
  }
  
  .form-input-error {
    @apply form-input border-error-300 text-error-900 placeholder-error-300 focus:ring-error-500 focus:border-error-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-error-600;
  }
  
  .form-help {
    @apply mt-1 text-sm text-gray-500;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
  }
  
  /* Alert Components */
  .alert {
    @apply p-4 rounded-md border;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-error {
    @apply alert bg-error-50 border-error-200 text-error-800;
  }
  
  .alert-info {
    @apply alert bg-ton-50 border-ton-200 text-ton-800;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .badge-info {
    @apply badge bg-ton-100 text-ton-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  /* Loading Components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-ton-600;
  }
  
  .spinner-sm {
    @apply spinner h-4 w-4;
  }
  
  .spinner-md {
    @apply spinner h-6 w-6;
  }
  
  .spinner-lg {
    @apply spinner h-8 w-8;
  }
  
  /* Skeleton Loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .skeleton-text {
    @apply skeleton h-4 w-full;
  }
  
  .skeleton-title {
    @apply skeleton h-6 w-3/4;
  }
  
  .skeleton-avatar {
    @apply skeleton h-10 w-10 rounded-full;
  }
  
  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-ton-600 to-ton-800 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-ton-600 to-ton-800;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-800;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
  }
  
  .border-gradient {
    @apply border-2 border-transparent bg-gradient-to-r from-ton-600 to-ton-800 bg-clip-border;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
  
  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Focus visible only */
  .focus-visible-only {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-ton-500 focus-visible:ring-offset-2;
  }
  
  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .dark {
    @apply bg-gray-900 text-gray-100;
  }
  
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark .form-input {
    @apply bg-gray-800 border-gray-600 text-gray-100 placeholder-gray-400;
  }
  
  .dark .form-label {
    @apply text-gray-300;
  }
}
