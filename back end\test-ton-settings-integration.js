/**
 * Test script to verify TON service integration with settings
 * This script tests that the TON service can load configuration from the database
 * Windows-friendly version with hardcoded local dev credentials
 */

const { Pool } = require('pg');

// Database configuration - hardcoded for local development
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'tonsite_dev',
  password: '1234',
  port: 5432,
  ssl: false
});

async function testTonSettingsIntegration() {
  console.log('🧪 Testing TON Service Settings Integration...\n');
  console.log('Database config:', {
    host: 'localhost',
    database: 'tonsite_dev',
    user: 'postgres',
    port: 5432
  });

  try {
    // Test 0: Basic connection test
    console.log('\n0. Testing database connection...');
    const connectionTest = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connection successful:', connectionTest.rows[0].current_time);

    // Test 1: Check if system_settings table exists and has blockchain settings
    console.log('\n1. Checking blockchain settings in database...');
    const settingsResult = await pool.query(
      "SELECT category, setting_key, setting_value FROM system_settings WHERE category = 'blockchain' ORDER BY setting_key"
    );

    if (settingsResult.rows.length === 0) {
      console.log('❌ No blockchain settings found in database');
      console.log('   Make sure the migration 004_add_system_settings_table.sql has been run');
      console.log('   You can run: npm run migrate or check the database/migrations folder');
      return false;
    }

    console.log('✅ Found blockchain settings:');
    settingsResult.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value}`);
    });

    // Test 2: Verify required blockchain settings exist
    console.log('\n2. Verifying required blockchain settings...');
    const requiredSettings = ['ton_network', 'ton_api_endpoint', 'confirmation_blocks', 'gas_limit'];
    const foundSettings = settingsResult.rows.map(row => row.setting_key);
    
    let allSettingsPresent = true;
    for (const setting of requiredSettings) {
      if (foundSettings.includes(setting)) {
        console.log(`✅ ${setting}: present`);
      } else {
        console.log(`❌ ${setting}: missing`);
        allSettingsPresent = false;
      }
    }

    if (!allSettingsPresent) {
      console.log('\n❌ Some required blockchain settings are missing');
      return false;
    }

    // Test 3: Test settings service blockchain method (if we can import it)
    console.log('\n3. Testing settings service blockchain method...');
    try {
      // This would require the TypeScript to be compiled, so we'll skip for now
      console.log('⏭️  Skipping TypeScript import test (requires compilation)');
    } catch (error) {
      console.log('⏭️  Could not test TypeScript imports:', error.message);
    }

    console.log('\n✅ TON Settings Integration Test PASSED');
    console.log('   The TON service should now be able to load configuration from the database');
    console.log('   instead of using hardcoded environment variables.');
    
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', {
      name: error.name,
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused - make sure PostgreSQL is running on localhost:5432');
      console.log('   On Windows, you can start PostgreSQL service from Services or pgAdmin');
    } else if (error.code === '28P01') {
      console.log('\n💡 Authentication failed - check database credentials');
      console.log('   Make sure user "postgres" exists with password "1234"');
      console.log('   Or update the credentials in this test script');
    } else if (error.code === '3D000') {
      console.log('\n💡 Database "tonsite_dev" does not exist');
      console.log('   Create the database first or check the database name');
    }

    return false;
  } finally {
    try {
      await pool.end();
    } catch (closeError) {
      console.error('Error closing pool:', closeError.message);
    }
  }
}

// Run the test
testTonSettingsIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test script error:', error);
    process.exit(1);
  });
