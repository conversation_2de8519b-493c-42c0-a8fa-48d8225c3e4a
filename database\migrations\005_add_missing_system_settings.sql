-- Migration: Add missing system settings
-- Description: Add all missing hardcoded configuration values to system_settings table

-- Insert missing settings that were identified as hardcoded values
INSERT INTO system_settings (category, setting_key, setting_value, data_type, description, is_sensitive, is_readonly) VALUES

-- Rate Limiting - Additional Settings
('rate_limiting', 'general_window_ms', '900000', 'number', 'General rate limit window in milliseconds (15 minutes)', false, false),
('rate_limiting', 'general_max_requests', '100', 'number', 'General rate limit max requests per window', false, false),
('rate_limiting', 'auth_window_ms', '900000', 'number', 'Auth rate limit window in milliseconds (15 minutes)', false, false),
('rate_limiting', 'auth_max_requests', '5', 'number', 'Auth rate limit max requests per window', false, false),
('rate_limiting', 'admin_window_ms', '900000', 'number', 'Admin rate limit window in milliseconds (15 minutes)', false, false),
('rate_limiting', 'admin_max_requests', '200', 'number', 'Admin rate limit max requests per window', false, false),
('rate_limiting', 'order_window_ms', '900000', 'number', 'Order rate limit window in milliseconds (15 minutes)', false, false),
('rate_limiting', 'payment_verify_window_ms', '300000', 'number', 'Payment verification rate limit window in milliseconds (5 minutes)', false, false),
('rate_limiting', 'payment_verify_requests_per_window', '20', 'number', 'Payment verification requests per window', false, false),
('rate_limiting', 'voucher_redeem_window_ms', '60000', 'number', 'Voucher redemption rate limit window in milliseconds (1 minute)', false, false),
('rate_limiting', 'voucher_redeem_requests_per_window', '10', 'number', 'Voucher redemption requests per window', false, false),
('rate_limiting', 'password_reset_window_ms', '3600000', 'number', 'Password reset rate limit window in milliseconds (1 hour)', false, false),
('rate_limiting', 'password_reset_requests_per_window', '3', 'number', 'Password reset requests per window', false, false),
('rate_limiting', 'speed_limiter_delay_after', '50', 'number', 'Number of requests before speed limiter adds delay', false, false),
('rate_limiting', 'speed_limiter_delay_ms', '500', 'number', 'Delay in milliseconds added by speed limiter', false, false),
('rate_limiting', 'speed_limiter_max_delay_ms', '20000', 'number', 'Maximum delay in milliseconds for speed limiter', false, false),

-- Security - Additional JWT and Session Settings
('security', 'jwt_access_expires_in', '15m', 'string', 'JWT access token expiration time', false, false),
('security', 'jwt_refresh_expires_in', '7d', 'string', 'JWT refresh token expiration time', false, false),
('security', 'bcrypt_rounds', '12', 'number', 'Number of bcrypt rounds for password hashing', false, false),
('security', 'session_max_age', '********', 'number', 'Session maximum age in milliseconds (24 hours)', false, false),
('security', 'lockout_duration', '1800000', 'number', 'Account lockout duration in milliseconds (30 minutes)', false, false),
('security', 'csrf_cookie_name', 'csrf-token', 'string', 'CSRF token cookie name', false, false),

-- Business Rules - Comprehensive Settings
('business_rules', 'voucher_code_length', '12', 'number', 'Length of generated voucher codes', false, false),
('business_rules', 'voucher_expiry_days', '365', 'number', 'Default voucher expiry in days', false, false),
('business_rules', 'max_vouchers_per_order', '10', 'number', 'Maximum vouchers per order', false, false),
('business_rules', 'min_order_amount', '0.01', 'string', 'Minimum order amount', false, false),
('business_rules', 'max_order_amount', '1000000', 'string', 'Maximum order amount', false, false),
('business_rules', 'order_timeout_minutes', '30', 'number', 'Order timeout in minutes', false, false),
('business_rules', 'payment_confirmation_blocks', '3', 'number', 'Required payment confirmation blocks', false, false),
('business_rules', 'payment_timeout_minutes', '30', 'number', 'Payment timeout in minutes', false, false),
('business_rules', 'email_verification_expires_hours', '24', 'number', 'Email verification expiry in hours', false, false),
('business_rules', 'password_reset_expires_hours', '1', 'number', 'Password reset expiry in hours', false, false),

-- File Upload Settings
('file_upload', 'max_file_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)', false, false),
('file_upload', 'upload_path', '/uploads', 'string', 'File upload directory path', false, false),
('file_upload', 'allowed_mime_types', '["image/jpeg", "image/png", "image/webp"]', 'json', 'Allowed MIME types for file uploads', false, false),

-- Pagination Settings
('pagination', 'default_page', '1', 'number', 'Default page number for pagination', false, false),
('pagination', 'default_limit', '20', 'number', 'Default items per page', false, false),
('pagination', 'max_limit', '100', 'number', 'Maximum items per page', false, false),

-- Cache Settings
('cache', 'user_session_ttl', '3600', 'number', 'User session cache TTL in seconds (1 hour)', false, false),
('cache', 'voucher_status_ttl', '300', 'number', 'Voucher status cache TTL in seconds (5 minutes)', false, false),
('cache', 'order_status_ttl', '120', 'number', 'Order status cache TTL in seconds (2 minutes)', false, false),
('cache', 'payment_status_ttl', '30', 'number', 'Payment status cache TTL in seconds', false, false),
('cache', 'admin_stats_ttl', '600', 'number', 'Admin stats cache TTL in seconds (10 minutes)', false, false),
('cache', 'settings_cache_ttl', '300', 'number', 'Settings cache TTL in seconds (5 minutes)', false, false),

-- Middleware Settings
('middleware', 'performance_monitoring_threshold', '2000', 'number', 'Performance monitoring threshold in milliseconds', false, false),
('middleware', 'request_size_monitoring_limit', '5242880', 'number', 'Request size monitoring limit in bytes (5MB)', false, false),
('middleware', 'request_size_limit', '10485760', 'number', 'Request size limit in bytes (10MB)', false, false),
('middleware', 'json_body_limit', '10485760', 'number', 'JSON body size limit in bytes (10MB)', false, false),
('middleware', 'memo_max_length', '500', 'number', 'Maximum memo field length', false, false),

-- TON Configuration - Additional Settings
('ton_config', 'mainnet_endpoint', 'https://toncenter.com/api/v2/jsonRPC', 'string', 'TON mainnet API endpoint', false, false),
('ton_config', 'testnet_endpoint', 'https://testnet.toncenter.com/api/v2/jsonRPC', 'string', 'TON testnet API endpoint', false, false),
('ton_config', 'default_gas_limit', '1000000', 'string', 'Default gas limit for TON transactions', false, false),
('ton_config', 'min_confirmation_blocks', '3', 'number', 'Minimum confirmation blocks for TON transactions', false, false),
('ton_config', 'supported_wallets', '["tonkeeper", "openmask", "tonhub"]', 'json', 'List of supported TON wallets', false, false),

-- Email Template Settings
('email_templates', 'welcome_template', 'welcome', 'string', 'Welcome email template name', false, false),
('email_templates', 'email_verification_template', 'email-verification', 'string', 'Email verification template name', false, false),
('email_templates', 'password_reset_template', 'password-reset', 'string', 'Password reset template name', false, false),
('email_templates', 'order_confirmation_template', 'order-confirmation', 'string', 'Order confirmation template name', false, false),
('email_templates', 'voucher_delivery_template', 'voucher-delivery', 'string', 'Voucher delivery template name', false, false),
('email_templates', 'payment_received_template', 'payment-received', 'string', 'Payment received template name', false, false)

ON CONFLICT (category, setting_key) DO NOTHING;

-- Add indexes for the new settings categories
CREATE INDEX IF NOT EXISTS idx_system_settings_rate_limiting ON system_settings(category) WHERE category = 'rate_limiting';
CREATE INDEX IF NOT EXISTS idx_system_settings_business_rules ON system_settings(category) WHERE category = 'business_rules';
CREATE INDEX IF NOT EXISTS idx_system_settings_file_upload ON system_settings(category) WHERE category = 'file_upload';
CREATE INDEX IF NOT EXISTS idx_system_settings_pagination ON system_settings(category) WHERE category = 'pagination';
CREATE INDEX IF NOT EXISTS idx_system_settings_cache ON system_settings(category) WHERE category = 'cache';
CREATE INDEX IF NOT EXISTS idx_system_settings_middleware ON system_settings(category) WHERE category = 'middleware';
CREATE INDEX IF NOT EXISTS idx_system_settings_ton_config ON system_settings(category) WHERE category = 'ton_config';
CREATE INDEX IF NOT EXISTS idx_system_settings_email_templates ON system_settings(category) WHERE category = 'email_templates';
