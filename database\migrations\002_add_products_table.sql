-- Add products table for shop functionality
-- Migration: 002_add_products_table.sql

-- Products table for voucher products catalog
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(50) PRIMARY KEY, -- Using string IDs like 'steam', 'netflix' for easy reference
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    min_amount DECIMAL(20,9) NOT NULL CHECK (min_amount > 0),
    max_amount DECIMAL(20,9) NOT NULL CHECK (max_amount >= min_amount),
    currency VARCHAR(10) DEFAULT 'TON' NOT NULL,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    review_count INTEGER DEFAULT 0 CHECK (review_count >= 0),
    image_url VARCHAR(500),
    popular BOOLEAN DEFAULT FALSE,
    available BOOLEAN DEFAULT TRUE,
    features J<PERSON>N<PERSON> DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb, -- For additional product-specific data
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product categories table for better organization
CREATE TABLE IF NOT EXISTS product_categories (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraint for product categories
ALTER TABLE products ADD CONSTRAINT fk_products_category 
    FOREIGN KEY (category) REFERENCES product_categories(id) ON UPDATE CASCADE;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_available ON products(available);
CREATE INDEX IF NOT EXISTS idx_products_popular ON products(popular);
CREATE INDEX IF NOT EXISTS idx_products_sort_order ON products(sort_order);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- Trigger for updated_at
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default categories
INSERT INTO product_categories (id, name, description, icon, sort_order) VALUES
    ('gaming', 'Gaming', 'Gaming platforms and gift cards', 'gamepad-2', 1),
    ('entertainment', 'Entertainment', 'Streaming and media services', 'tv', 2),
    ('software', 'Software', 'Software licenses and subscriptions', 'code', 3),
    ('retail', 'Retail', 'Shopping and retail gift cards', 'shopping-bag', 4)
ON CONFLICT (id) DO NOTHING;

-- Insert default products (matching frontend data)
INSERT INTO products (id, name, description, category, min_amount, max_amount, rating, review_count, image_url, popular, features) VALUES
    ('steam', 'Steam Gift Card', 'Digital gift cards for the world''s largest gaming platform. Access thousands of games, DLCs, and software.', 'gaming', 5, 500, 4.9, 1250, '/images/steam-card.jpg', true, '["Instant delivery", "No expiration", "Global redemption", "24/7 support"]'::jsonb),
    ('netflix', 'Netflix Gift Card', 'Stream unlimited movies and TV shows with Netflix gift cards. Perfect for entertainment lovers.', 'entertainment', 10, 200, 4.8, 980, '/images/netflix-card.jpg', true, '["Instant delivery", "HD streaming", "Multiple devices", "Original content"]'::jsonb),
    ('spotify', 'Spotify Premium', 'Enjoy ad-free music streaming with Spotify Premium gift cards. Millions of songs at your fingertips.', 'entertainment', 5, 100, 4.7, 850, '/images/spotify-card.jpg', false, '["Ad-free music", "Offline downloads", "High quality audio", "Podcast access"]'::jsonb),
    ('amazon', 'Amazon Gift Card', 'Shop millions of products on Amazon with digital gift cards. The ultimate shopping experience.', 'retail', 10, 1000, 4.9, 2100, '/images/amazon-card.jpg', true, '["Instant delivery", "No expiration", "Millions of products", "Prime eligible"]'::jsonb),
    ('playstation', 'PlayStation Store', 'PlayStation Store gift cards for games, DLCs, and PlayStation Plus subscriptions.', 'gaming', 10, 300, 4.8, 1100, '/images/playstation-card.jpg', false, '["Instant delivery", "PS4 & PS5 games", "PlayStation Plus", "Exclusive content"]'::jsonb),
    ('nintendo', 'Nintendo eShop', 'Nintendo eShop gift cards for Switch games, DLCs, and digital content.', 'gaming', 10, 300, 4.8, 720, '/images/nintendo-card.jpg', false, '["Instant delivery", "Switch compatible", "Digital games", "Family friendly"]'::jsonb)
ON CONFLICT (id) DO NOTHING;

-- Add order_items table to track which products were ordered
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(50) NOT NULL REFERENCES products(id),
    quantity INTEGER DEFAULT 1 CHECK (quantity > 0),
    unit_amount DECIMAL(20,9) NOT NULL CHECK (unit_amount > 0),
    total_amount DECIMAL(20,9) NOT NULL CHECK (total_amount > 0),
    product_data JSONB, -- Snapshot of product data at time of order
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for order_items
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- Update orders table to support product-based orders
ALTER TABLE orders ADD COLUMN IF NOT EXISTS product_id VARCHAR(50) REFERENCES products(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS quantity INTEGER DEFAULT 1 CHECK (quantity > 0);

-- Index for product_id in orders
CREATE INDEX IF NOT EXISTS idx_orders_product_id ON orders(product_id);
