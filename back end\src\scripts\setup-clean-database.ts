import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';

// Direct database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

const setupCleanDatabase = async () => {
  const db = new Pool(dbConfig);
  
  try {
    console.log('🚀 Setting up clean database (no test data)...');

    // Test database connection
    await db.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Run migrations in order
    const migrationsDir = path.join(process.cwd(), '..', '..', '..', 'database', 'migrations');
    console.log(`📁 Looking for migrations in: ${migrationsDir}`);

    if (!fs.existsSync(migrationsDir)) {
      console.log('❌ Migrations directory not found');
      return;
    }

    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure proper order

    console.log(`📦 Found ${migrationFiles.length} migration files:`, migrationFiles);

    for (const migrationFile of migrationFiles) {
      console.log(`🔄 Running migration: ${migrationFile}`);
      const migrationPath = path.join(migrationsDir, migrationFile);
      
      try {
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Execute the entire migration file as one query to handle functions properly
        try {
          await db.query(migrationSQL);
        } catch (error: any) {
          // Ignore "already exists" errors for idempotent migrations
          if (!error.message.includes('already exists') &&
              !error.message.includes('duplicate key') &&
              !error.message.includes('relation') &&
              !error.message.includes('does not exist')) {
            console.error(`❌ Migration error in ${migrationFile}:`, error.message);
            throw error;
          }
        }
        console.log(`✅ Migration completed: ${migrationFile}`);
      } catch (error: any) {
        console.error(`❌ Failed to run migration ${migrationFile}:`, error.message);
        throw error;
      }
    }

    // Verify essential tables exist
    console.log('🔍 Verifying essential tables...');
    const essentialTables = [
      'users',
      'orders',
      'vouchers',
      'products',
      'product_categories',
      'user_settings',
      'system_settings',
      'system_settings_log',
      'admin_logs',
      'user_sessions',
      'transactions'
    ];

    for (const table of essentialTables) {
      try {
        const result = await db.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);
        
        if (result.rows[0].exists) {
          console.log(`✅ Table exists: ${table}`);
        } else {
          console.log(`❌ Missing table: ${table}`);
        }
      } catch (error: any) {
        console.error(`❌ Error checking table ${table}:`, error.message);
      }
    }

    // Show table counts (should all be 0 for clean database)
    console.log('📊 Table row counts:');
    for (const table of essentialTables) {
      try {
        const result = await db.query(`SELECT COUNT(*) as count FROM ${table};`);
        const count = result.rows[0].count;
        console.log(`   ${table}: ${count} rows`);
      } catch (error: any) {
        console.log(`   ${table}: Error - ${error.message}`);
      }
    }

    console.log('🎉 Clean database setup completed successfully!');
    console.log('📝 Database is ready with schema but contains NO test data');
    
  } catch (error) {
    console.error('❌ Error setting up clean database:', error);
    throw error;
  } finally {
    await db.end();
  }
};

// Run the setup
setupCleanDatabase()
  .then(() => {
    console.log('✅ Clean database setup finished');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Clean database setup failed:', error);
    process.exit(1);
  });
