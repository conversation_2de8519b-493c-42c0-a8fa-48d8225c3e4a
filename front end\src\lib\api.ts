import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Token management
let accessToken: string | null = null;
let refreshToken: string | null = null;

export const setTokens = (tokens: { accessToken: string; refreshToken: string }) => {
  accessToken = tokens.accessToken;
  refreshToken = tokens.refreshToken;
  
  // Store in localStorage for persistence
  if (typeof window !== 'undefined') {
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
  }
};

export const getTokens = () => {
  if (typeof window !== 'undefined') {
    return {
      accessToken: localStorage.getItem('accessToken'),
      refreshToken: localStorage.getItem('refreshToken'),
    };
  }
  return { accessToken: null, refreshToken: null };
};

export const clearTokens = () => {
  accessToken = null;
  refreshToken = null;
  
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }
};

// Initialize tokens from localStorage
if (typeof window !== 'undefined') {
  const tokens = getTokens();
  accessToken = tokens.accessToken;
  refreshToken = tokens.refreshToken;
}

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh-token`, {
            refreshToken,
          });

          const { accessToken: newAccessToken, refreshToken: newRefreshToken } = response.data.data;
          setTokens({ accessToken: newAccessToken, refreshToken: newRefreshToken });

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, redirect to login
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }
    }

    // Handle other errors
    if (error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else if (error.message) {
      toast.error(error.message);
    } else {
      toast.error('An unexpected error occurred');
    }

    return Promise.reject(error);
  }
);

// API response interface
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Authentication API
export const authApi = {
  register: async (data: {
    email: string;
    telegramId: string;
    password: string;
    memo?: string;
  }): Promise<ApiResponse> => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  login: async (data: {
    email: string;
    password: string;
  }): Promise<ApiResponse> => {
    const response = await api.post('/auth/login', data);
    if (response.data.success && response.data.data.tokens) {
      setTokens(response.data.data.tokens);
    }
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    try {
      const response = await api.post('/auth/logout');
      clearTokens();
      return response.data;
    } catch (error) {
      clearTokens();
      throw error;
    }
  },

  verifyEmail: async (token: string): Promise<ApiResponse> => {
    const response = await api.post('/auth/verify-email', { token });
    return response.data;
  },

  resendVerification: async (email: string): Promise<ApiResponse> => {
    const response = await api.post('/auth/resend-verification', { email });
    return response.data;
  },

  requestPasswordReset: async (email: string): Promise<ApiResponse> => {
    const response = await api.post('/auth/request-password-reset', { email });
    return response.data;
  },

  resetPassword: async (data: {
    token: string;
    newPassword: string;
  }): Promise<ApiResponse> => {
    const response = await api.post('/auth/reset-password', data);
    return response.data;
  },

  getMe: async (): Promise<ApiResponse> => {
    const response = await api.get('/auth/me');
    return response.data;
  },
};

// User API
export const userApi = {
  getProfile: async (): Promise<ApiResponse> => {
    const response = await api.get('/user/profile');
    return response.data;
  },

  updateProfile: async (data: any): Promise<ApiResponse> => {
    const response = await api.put('/user/profile', data);
    return response.data;
  },

  getOrders: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/user/orders', { params });
    return response.data;
  },

  getVouchers: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/user/vouchers', { params });
    return response.data;
  },
};

// Orders API
export const ordersApi = {
  create: async (data: {
    productId: string;
    amount: number;
    currency: string;
    quantity?: number;
    memo?: string;
  }): Promise<ApiResponse> => {
    const response = await api.post('/orders', data);
    return response.data;
  },

  getById: async (id: string): Promise<ApiResponse> => {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },

  getAll: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/orders', { params });
    return response.data;
  },
};

// Payments API
export const paymentsApi = {
  initiate: async (orderId: string): Promise<ApiResponse> => {
    const response = await api.post('/payments/initiate', { orderId });
    return response.data;
  },

  verify: async (data: {
    transactionHash: string;
    orderId: string;
  }): Promise<ApiResponse> => {
    const response = await api.post('/payments/verify', data);
    return response.data;
  },

  getStatus: async (paymentId: string): Promise<ApiResponse> => {
    const response = await api.get(`/payments/${paymentId}/status`);
    return response.data;
  },
};

// Vouchers API
export const vouchersApi = {
  redeem: async (code: string): Promise<ApiResponse> => {
    const response = await api.post('/vouchers/redeem', { code });
    return response.data;
  },

  getStatus: async (code: string): Promise<ApiResponse> => {
    const response = await api.get(`/vouchers/${code}/status`);
    return response.data;
  },

  getAll: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/vouchers', { params });
    return response.data;
  },
};

// Admin API
export const adminApi = {
  getUsers: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/admin/users', { params });
    return response.data;
  },

  getOrders: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/admin/orders', { params });
    return response.data;
  },

  getVouchers: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/admin/vouchers', { params });
    return response.data;
  },

  getStats: async (): Promise<ApiResponse> => {
    const response = await api.get('/admin/stats');
    return response.data;
  },

  bulkVouchers: async (data: any): Promise<ApiResponse> => {
    const response = await api.post('/admin/vouchers/bulk', data);
    return response.data;
  },

  bulkCreateVouchers: async (data: any): Promise<ApiResponse> => {
    const response = await api.post('/admin/vouchers/bulk', data);
    return response.data;
  },

  createVoucher: async (data: any): Promise<ApiResponse> => {
    const response = await api.post('/admin/vouchers', data);
    return response.data;
  },

  updateVoucherStatus: async (voucherId: string, status: string): Promise<ApiResponse> => {
    const response = await api.patch(`/admin/vouchers/${voucherId}/status`, { status });
    return response.data;
  },

  // Product Management
  getProducts: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/admin/products', { params });
    return response.data;
  },

  getProductById: async (productId: string): Promise<ApiResponse> => {
    const response = await api.get(`/admin/products/${productId}`);
    return response.data;
  },

  createProduct: async (data: any): Promise<ApiResponse> => {
    const response = await api.post('/admin/products', data);
    return response.data;
  },

  updateProduct: async (productId: string, data: any): Promise<ApiResponse> => {
    const response = await api.put(`/admin/products/${productId}`, data);
    return response.data;
  },

  deleteProduct: async (productId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/admin/products/${productId}`);
    return response.data;
  },

  getCategories: async (): Promise<ApiResponse> => {
    const response = await api.get('/admin/categories');
    return response.data;
  },

  createCategory: async (data: any): Promise<ApiResponse> => {
    const response = await api.post('/admin/categories', data);
    return response.data;
  },

  // System Settings Management
  getSystemSettings: async (): Promise<ApiResponse> => {
    const response = await api.get('/admin/settings');
    return response.data;
  },

  updateSystemSettings: async (data: { settings: any; changeReason?: string }): Promise<ApiResponse> => {
    const response = await api.put('/admin/settings', data);
    return response.data;
  },

  getSettingsHistory: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/admin/settings/history', { params });
    return response.data;
  },

  resetSettingsToDefault: async (data: { category?: string; confirmReset: boolean }): Promise<ApiResponse> => {
    const response = await api.post('/admin/settings/reset', data);
    return response.data;
  },
};

// TON API
export const tonApi = {
  getBalance: async (address: string): Promise<ApiResponse> => {
    const response = await api.get(`/ton/balance/${address}`);
    return response.data;
  },

  getPrice: async (): Promise<ApiResponse> => {
    const response = await api.get('/ton/price');
    return response.data;
  },

  validateAddress: async (address: string): Promise<ApiResponse> => {
    const response = await api.post('/ton/validate-address', { address });
    return response.data;
  },

  getTransaction: async (hash: string): Promise<ApiResponse> => {
    const response = await api.get(`/ton/transaction/${hash}`);
    return response.data;
  },

  convertAmount: async (data: { amount: string; from: string; to: string }): Promise<ApiResponse> => {
    const response = await api.post('/ton/convert', data);
    return response.data;
  },

  getHealth: async (): Promise<ApiResponse> => {
    const response = await api.get('/ton/health');
    return response.data;
  },

  getNetwork: async (): Promise<ApiResponse> => {
    const response = await api.get('/ton/network');
    return response.data;
  },
};

// Shop API (Public)
export const shopApi = {
  getProducts: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/shop/products', { params });
    return response.data;
  },

  getProductById: async (productId: string): Promise<ApiResponse> => {
    const response = await api.get(`/shop/products/${productId}`);
    return response.data;
  },

  getCategories: async (): Promise<ApiResponse> => {
    const response = await api.get('/shop/categories');
    return response.data;
  },

  getFeaturedProducts: async (limit?: number): Promise<ApiResponse> => {
    const response = await api.get('/shop/featured', { params: { limit } });
    return response.data;
  },

  searchProducts: async (query: string, limit?: number): Promise<ApiResponse> => {
    const response = await api.get('/shop/search', { params: { q: query, limit } });
    return response.data;
  },
};

// Webhook API
export const webhookApi = {
  verifyTransaction: async (data: { transactionHash: string; orderId: string }): Promise<ApiResponse> => {
    const response = await api.post('/webhooks/verify-transaction', data);
    return response.data;
  },

  getLogs: async (params?: any): Promise<ApiResponse> => {
    const response = await api.get('/webhooks/logs', { params });
    return response.data;
  },
};

// Public Settings API (minimal secure data)
export const publicSettingsApi = {
  getAll: async (): Promise<ApiResponse> => {
    const response = await api.get('/public/settings');
    return response.data;
  },

  getByCategory: async (category: 'payment' | 'voucher' | 'security' | 'system'): Promise<ApiResponse> => {
    const response = await api.get(`/public/settings/${category}`);
    return response.data;
  },
};

// Authenticated Settings API (secure replacement for public settings)
export const authSettingsApi = {
  getAll: async (): Promise<ApiResponse> => {
    const response = await api.get('/auth/settings');
    return response.data;
  },
};

export default api;
