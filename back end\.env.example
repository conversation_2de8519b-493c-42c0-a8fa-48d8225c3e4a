# Environment Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=postgresql://postgres:1234@localhost:5432/tonsite
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=tonsite_dev
DATABASE_USER=postgres
DATABASE_PASSWORD=1234
DATABASE_SSL=false

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
SESSION_MAX_AGE=86400000

# CSRF Configuration
CSRF_SECRET=your-super-secret-csrf-key-change-this-in-production

# TON Configuration
TON_NETWORK=testnet
TON_API_KEY=your-ton-api-key
TON_ENDPOINT=https://testnet.toncenter.com/api/v2/jsonRPC
TON_WALLET_MNEMONIC=your wallet mnemonic words here
TON_MASTER_WALLET_ADDRESS=your-master-wallet-address

# Email Configuration
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM_NAME=TON Voucher Platform
EMAIL_FROM_ADDRESS=<EMAIL>

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=1800000
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Admin Configuration (Environment-based for security)
ADMIN_EMAIL=<EMAIL>
ADMIN_TELEGRAM_ID=admin_dev
ADMIN_PASSWORD=DevAdmin123!
ADMIN_SETUP_REQUIRED=true

# Business Rules
VOUCHER_EXPIRY_DAYS=365
ORDER_TIMEOUT_MINUTES=30
PAYMENT_TIMEOUT_MINUTES=30
MIN_ORDER_AMOUNT=0.01
MAX_ORDER_AMOUNT=1000000

# Monitoring and Health
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# Development Only
DEBUG=tonsite:*
ENABLE_SWAGGER=true
ENABLE_CORS_DEBUG=false
