{"name": "@tonsite/frontend", "version": "1.0.0", "description": "Frontend application for TON Voucher Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@ton/core": "^0.56.0", "@ton/crypto": "^3.2.0", "@ton/ton": "^13.9.0", "@tonconnect/sdk": "^3.0.5", "@tonconnect/ui-react": "^2.2.0", "@tonsite/shared": "file:../shared", "axios": "^1.6.2", "clsx": "^2.0.0", "copy-to-clipboard": "^3.3.3", "dompurify": "^3.0.7", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "^14.0.4", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "tailwind-merge": "^2.2.0", "typescript": "^5.3.3", "validator": "^13.11.0", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.8", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.4", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/validator": "^13.11.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}