'use client';

import { useState, useEffect } from 'react';
import {
  Bell,
  Shield,
  Palette,
  Globe,
  Smartphone,
  Mail,
  Lock,
  Eye,
  Download,
  Trash2,
} from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

interface NotificationSettings {
  email: boolean;
  push: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  security: boolean;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  dataSharing: boolean;
  analytics: boolean;
}

export default function SettingsPage() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationSettings>({
    email: true,
    push: true,
    orderUpdates: true,
    promotions: false,
    security: true,
  });

  const [privacy, setPrivacy] = useState<PrivacySettings>({
    profileVisibility: 'private',
    dataSharing: false,
    analytics: true,
  });

  const [theme, setTheme] = useState('system');
  const [language, setLanguage] = useState('en');
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/v1/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setNotifications(data.data.settings.notifications);
          setPrivacy(data.data.settings.privacy);
          setTheme(data.data.settings.appearance.theme);
          setLanguage(data.data.settings.appearance.language);
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationChange = (key: keyof NotificationSettings) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handlePrivacyChange = (key: keyof PrivacySettings, value: any) => {
    setPrivacy(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('Please log in to save settings');
        return;
      }

      const response = await fetch('/api/v1/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notifications,
          privacy,
          appearance: { theme, language },
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast.success('Settings saved successfully!');
        } else {
          toast.error(data.error || 'Failed to save settings');
        }
      } else {
        toast.error('Failed to save settings');
      }
    } catch (error) {
      toast.error('Failed to save settings');
      console.error('Settings save error:', error);
    } finally {
      setSaving(false);
    }
  };

  const exportData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('Please log in to export data');
        return;
      }

      const response = await fetch('/api/v1/settings/export', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Create and download the export file
          const blob = new Blob([JSON.stringify(data.data, null, 2)], {
            type: 'application/json',
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          toast.success('Data exported successfully!');
        } else {
          toast.error(data.error || 'Failed to export data');
        }
      } else {
        toast.error('Failed to export data');
      }
    } catch (error) {
      toast.error('Failed to export data');
      console.error('Export error:', error);
    }
  };

  const deleteAccount = async () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        // TODO: Replace with actual API call
        toast.error('Account deletion is not yet implemented');
      } catch (error) {
        toast.error('Failed to delete account');
      }
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account preferences and privacy settings</p>
          </div>
          <button
            onClick={saveSettings}
            disabled={saving}
            className="btn-primary"
          >
            {saving ? (
              <>
                <div className="spinner-sm mr-2" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Notifications */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-ton-600" />
                <h2 className="text-lg font-medium text-gray-900">Notifications</h2>
              </div>
              <p className="text-sm text-gray-600">Choose how you want to be notified</p>
            </div>
            <div className="card-body space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Email Notifications</p>
                  <p className="text-sm text-gray-600">Receive notifications via email</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notifications.email}
                    onChange={() => handleNotificationChange('email')}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Push Notifications</p>
                  <p className="text-sm text-gray-600">Receive browser push notifications</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notifications.push}
                    onChange={() => handleNotificationChange('push')}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Order Updates</p>
                  <p className="text-sm text-gray-600">Get notified about order status changes</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notifications.orderUpdates}
                    onChange={() => handleNotificationChange('orderUpdates')}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Promotions</p>
                  <p className="text-sm text-gray-600">Receive promotional offers and news</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notifications.promotions}
                    onChange={() => handleNotificationChange('promotions')}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Security Alerts</p>
                  <p className="text-sm text-gray-600">Important security notifications</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notifications.security}
                    onChange={() => handleNotificationChange('security')}
                  />
                  <span className="slider"></span>
                </label>
              </div>
            </div>
          </div>

          {/* Privacy & Security */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-ton-600" />
                <h2 className="text-lg font-medium text-gray-900">Privacy & Security</h2>
              </div>
              <p className="text-sm text-gray-600">Control your privacy and security settings</p>
            </div>
            <div className="card-body space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Profile Visibility
                </label>
                <select
                  value={privacy.profileVisibility}
                  onChange={(e) => handlePrivacyChange('profileVisibility', e.target.value)}
                  className="input"
                >
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Data Sharing</p>
                  <p className="text-sm text-gray-600">Share anonymized data for improvements</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={privacy.dataSharing}
                    onChange={() => handlePrivacyChange('dataSharing', !privacy.dataSharing)}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Analytics</p>
                  <p className="text-sm text-gray-600">Help us improve with usage analytics</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={privacy.analytics}
                    onChange={() => handlePrivacyChange('analytics', !privacy.analytics)}
                  />
                  <span className="slider"></span>
                </label>
              </div>

              <div className="pt-4 border-t space-y-3">
                <button className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center gap-3">
                    <Lock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">Change Password</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </button>
                <button className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center gap-3">
                    <Smartphone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">Two-Factor Authentication</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </button>
              </div>
            </div>
          </div>

          {/* Appearance */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-ton-600" />
                <h2 className="text-lg font-medium text-gray-900">Appearance</h2>
              </div>
              <p className="text-sm text-gray-600">Customize the look and feel</p>
            </div>
            <div className="card-body space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Theme
                </label>
                <select
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="input"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="system">System</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Language
                </label>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="input"
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="ru">Русский</option>
                </select>
              </div>
            </div>
          </div>

          {/* Data & Account */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Download className="h-5 w-5 text-ton-600" />
                <h2 className="text-lg font-medium text-gray-900">Data & Account</h2>
              </div>
              <p className="text-sm text-gray-600">Manage your data and account</p>
            </div>
            <div className="card-body space-y-4">
              <button
                onClick={exportData}
                className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Download className="h-4 w-4 text-gray-400" />
                  <div className="text-left">
                    <p className="text-sm font-medium text-gray-900">Export Data</p>
                    <p className="text-xs text-gray-600">Download all your account data</p>
                  </div>
                </div>
                <span className="text-gray-400">→</span>
              </button>

              <div className="pt-4 border-t">
                <button
                  onClick={deleteAccount}
                  className="w-full flex items-center justify-between p-3 hover:bg-red-50 rounded-lg transition-colors text-red-600"
                >
                  <div className="flex items-center gap-3">
                    <Trash2 className="h-4 w-4" />
                    <div className="text-left">
                      <p className="text-sm font-medium">Delete Account</p>
                      <p className="text-xs text-red-500">Permanently delete your account and data</p>
                    </div>
                  </div>
                  <span className="text-red-400">→</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
