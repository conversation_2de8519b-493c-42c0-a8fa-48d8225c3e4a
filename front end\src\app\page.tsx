'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Shield, Zap, Gift, Users, ArrowRight, CheckCircle } from 'lucide-react';

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(false);

  const features = [
    {
      icon: Shield,
      title: 'Secure Payments',
      description: 'Military-grade security with TON blockchain technology for safe transactions.',
    },
    {
      icon: Zap,
      title: 'Instant Delivery',
      description: 'Get your voucher codes immediately after payment confirmation.',
    },
    {
      icon: Gift,
      title: 'Premium Vouchers',
      description: 'Exclusive access to premium game content and special edition items.',
    },
    {
      icon: Users,
      title: 'Trusted Platform',
      description: 'Join thousands of satisfied customers who trust our platform.',
    },
  ];

  const benefits = [
    'Cryptocurrency payments with TON',
    'Instant voucher code delivery',
    'Email notifications and confirmations',
    'Secure redemption tracking',
    'Premium customer support',
    '365-day voucher validity',
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gradient">TON Vouchers</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/login"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/auth/register"
                className="btn-primary"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Premium Game Vouchers
              <span className="block text-gradient">Powered by TON</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Secure, instant, and reliable. Purchase premium game voucher codes with 
              cryptocurrency and get instant access to exclusive content.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/register"
                className="btn-primary btn-xl"
              >
                Start Shopping
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link
                href="#features"
                className="btn-outline btn-xl"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
        
        {/* Background decoration */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-ton-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
          <div className="absolute top-0 right-1/4 transform w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow animation-delay-2000"></div>
          <div className="absolute bottom-0 left-1/4 transform w-96 h-96 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow animation-delay-4000"></div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Platform?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the future of digital voucher purchases with blockchain security and instant delivery.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="card hover:shadow-medium transition-shadow duration-300"
              >
                <div className="card-body text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-ton-100 rounded-lg mb-4">
                    <feature.icon className="h-6 w-6 text-ton-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Everything You Need for Secure Voucher Purchases
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Our platform combines the security of blockchain technology with the convenience 
                of instant digital delivery. Get premium game content with confidence.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-success-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-8">
                <Link
                  href="/auth/register"
                  className="btn-primary btn-lg"
                >
                  Get Started Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
            
            <div className="relative">
              <div className="card">
                <div className="card-body">
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-ton-100 rounded-full mb-4">
                      <Gift className="h-8 w-8 text-ton-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Premium Game Pass
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Exclusive access to premium content
                    </p>
                    <div className="text-3xl font-bold text-ton-600 mb-4">
                      25.00 TON
                    </div>
                    <button
                      className="btn-primary w-full"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <div className="spinner-sm mr-2" />
                      ) : null}
                      Purchase Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-ton-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers and experience the future of digital voucher purchases.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/register"
              className="btn bg-white text-ton-600 hover:bg-gray-50 btn-xl"
            >
              Create Account
            </Link>
            <Link
              href="/auth/login"
              className="btn border-white text-white hover:bg-white hover:text-ton-600 btn-xl"
            >
              Sign In
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">TON Vouchers</h3>
              <p className="text-gray-400 mb-4">
                Secure e-commerce platform for premium game voucher codes with TON cryptocurrency payments.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Platform</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/auth/register" className="hover:text-white transition-colors">Get Started</Link></li>
                <li><Link href="/auth/login" className="hover:text-white transition-colors">Sign In</Link></li>
                <li><Link href="/support" className="hover:text-white transition-colors">Support</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/security" className="hover:text-white transition-colors">Security</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 TON Voucher Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
