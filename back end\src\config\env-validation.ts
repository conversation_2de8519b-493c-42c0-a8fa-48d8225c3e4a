/**
 * Environment Variable Validation
 * 
 * This module validates all required environment variables and provides
 * type-safe access to configuration values. It's designed to:
 * - Fail fast if required variables are missing
 * - Provide sensible defaults for development
 * - Enforce strict validation in production
 * - Give clear error messages for misconfiguration
 */

import { logger } from './logger';

interface EnvironmentConfig {
  // Server
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;

  // Database
  DATABASE_HOST: string;
  DATABASE_PORT: number;
  DATABASE_NAME: string;
  DATABASE_USER: string;
  DATABASE_PASSWORD: string;
  DATABASE_SSL: boolean;

  // Redis
  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_PASSWORD: string;
  REDIS_DB: number;

  // JWT
  JWT_SECRET: string;
  JWT_ACCESS_EXPIRES_IN: string;
  JWT_REFRESH_EXPIRES_IN: string;

  // Security
  BCRYPT_ROUNDS: number;
  MAX_LOGIN_ATTEMPTS: number;
  LOCKOUT_DURATION: number;

  // Admin
  ADMIN_EMAIL: string;
  ADMIN_TELEGRAM_ID: string;
  ADMIN_PASSWORD: string;
  ADMIN_SETUP_REQUIRED: boolean;

  // TON
  TON_NETWORK: 'mainnet' | 'testnet';
  TON_API_KEY: string;
  TON_ENDPOINT: string;

  // Email
  EMAIL_SMTP_HOST: string;
  EMAIL_SMTP_PORT: number;
  EMAIL_USER: string;
  EMAIL_PASS: string;
}

/**
 * Validate environment variables and return typed configuration
 */
export const validateEnvironment = (): EnvironmentConfig => {
  const env = process.env;
  const isProduction = env.NODE_ENV === 'production';
  const isDevelopment = env.NODE_ENV === 'development' || !env.NODE_ENV;

  // Helper function to get required variable
  const getRequired = (key: string, defaultValue?: string): string => {
    const value = env[key] || defaultValue;
    if (!value) {
      throw new Error(`Required environment variable ${key} is not set`);
    }
    return value;
  };

  // Helper function to get optional variable with default
  const getOptional = (key: string, defaultValue: string): string => {
    return env[key] || defaultValue;
  };

  // Helper function to parse integer
  const getInt = (key: string, defaultValue: number): number => {
    const value = env[key];
    if (!value) return defaultValue;
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      throw new Error(`Environment variable ${key} must be a valid integer`);
    }
    return parsed;
  };

  // Helper function to parse boolean
  const getBool = (key: string, defaultValue: boolean): boolean => {
    const value = env[key];
    if (!value) return defaultValue;
    return value.toLowerCase() === 'true';
  };

  try {
    // Validate NODE_ENV
    const nodeEnv = env.NODE_ENV as EnvironmentConfig['NODE_ENV'];
    if (!['development', 'production', 'test'].includes(nodeEnv)) {
      throw new Error('NODE_ENV must be one of: development, production, test');
    }

    // Database configuration
    const databaseConfig = {
      DATABASE_HOST: getRequired('DATABASE_HOST', isDevelopment ? 'localhost' : undefined),
      DATABASE_PORT: getInt('DATABASE_PORT', 5432),
      DATABASE_NAME: getRequired('DATABASE_NAME', isDevelopment ? 'tonsite_dev' : undefined),
      DATABASE_USER: getRequired('DATABASE_USER', isDevelopment ? 'postgres' : undefined),
      DATABASE_PASSWORD: getRequired('DATABASE_PASSWORD', isDevelopment ? '' : undefined),
      DATABASE_SSL: getBool('DATABASE_SSL', isProduction),
    };

    // Redis configuration
    const redisConfig = {
      REDIS_HOST: getRequired('REDIS_HOST', isDevelopment ? 'localhost' : undefined),
      REDIS_PORT: getInt('REDIS_PORT', 6379),
      REDIS_PASSWORD: getOptional('REDIS_PASSWORD', ''), // Allow empty password for development
      REDIS_DB: getInt('REDIS_DB', 0),
    };

    // JWT configuration
    const jwtSecret = getRequired('JWT_SECRET', isDevelopment ? 'dev-jwt-secret-min-32-chars-long' : undefined);
    if (isProduction && jwtSecret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters in production');
    }

    const jwtConfig = {
      JWT_SECRET: jwtSecret,
      JWT_ACCESS_EXPIRES_IN: getOptional('JWT_ACCESS_EXPIRES_IN', '15m'),
      JWT_REFRESH_EXPIRES_IN: getOptional('JWT_REFRESH_EXPIRES_IN', '7d'),
    };

    // Security configuration
    const securityConfig = {
      BCRYPT_ROUNDS: getInt('BCRYPT_ROUNDS', isProduction ? 14 : 12),
      MAX_LOGIN_ATTEMPTS: getInt('MAX_LOGIN_ATTEMPTS', isProduction ? 3 : 5),
      LOCKOUT_DURATION: getInt('LOCKOUT_DURATION', isProduction ? 3600000 : 1800000),
    };

    // Admin configuration
    const adminPassword = getRequired('ADMIN_PASSWORD', isDevelopment ? 'DevAdmin123!' : undefined);
    if (isProduction) {
      if (adminPassword.length < 12) {
        throw new Error('ADMIN_PASSWORD must be at least 12 characters in production');
      }
      if (['Admin123!', 'DevAdmin123!', 'Password123!'].includes(adminPassword)) {
        throw new Error('Cannot use default admin password in production');
      }
    }

    const adminConfig = {
      ADMIN_EMAIL: getRequired('ADMIN_EMAIL', isDevelopment ? '<EMAIL>' : undefined),
      ADMIN_TELEGRAM_ID: getRequired('ADMIN_TELEGRAM_ID', isDevelopment ? 'admin_dev' : undefined),
      ADMIN_PASSWORD: adminPassword,
      ADMIN_SETUP_REQUIRED: getBool('ADMIN_SETUP_REQUIRED', true),
    };

    // TON configuration
    const tonNetwork = env.TON_NETWORK as EnvironmentConfig['TON_NETWORK'];
    if (!['mainnet', 'testnet'].includes(tonNetwork || '')) {
      throw new Error('TON_NETWORK must be either "mainnet" or "testnet"');
    }

    const tonConfig = {
      TON_NETWORK: tonNetwork || (isProduction ? 'mainnet' : 'testnet'),
      TON_API_KEY: getRequired('TON_API_KEY', isDevelopment ? 'dev-api-key' : undefined),
      TON_ENDPOINT: getRequired('TON_ENDPOINT', 
        isDevelopment ? 'https://testnet.toncenter.com/api/v2/jsonRPC' : undefined),
    };

    // Email configuration
    const emailConfig = {
      EMAIL_SMTP_HOST: getRequired('EMAIL_SMTP_HOST', isDevelopment ? 'smtp.gmail.com' : undefined),
      EMAIL_SMTP_PORT: getInt('EMAIL_SMTP_PORT', 587),
      EMAIL_USER: getRequired('EMAIL_USER', isDevelopment ? '<EMAIL>' : undefined),
      EMAIL_PASS: getRequired('EMAIL_PASS', isDevelopment ? 'dev-email-pass' : undefined),
    };

    const config: EnvironmentConfig = {
      NODE_ENV: nodeEnv,
      PORT: getInt('PORT', 3001),
      ...databaseConfig,
      ...redisConfig,
      ...jwtConfig,
      ...securityConfig,
      ...adminConfig,
      ...tonConfig,
      ...emailConfig,
    };

    // Log configuration summary (without sensitive data)
    logger.info('Environment configuration validated:', {
      NODE_ENV: config.NODE_ENV,
      PORT: config.PORT,
      DATABASE_HOST: config.DATABASE_HOST,
      DATABASE_SSL: config.DATABASE_SSL,
      REDIS_HOST: config.REDIS_HOST,
      TON_NETWORK: config.TON_NETWORK,
      ADMIN_EMAIL: config.ADMIN_EMAIL,
      hasJwtSecret: !!config.JWT_SECRET,
      hasAdminPassword: !!config.ADMIN_PASSWORD,
    });

    return config;

  } catch (error) {
    logger.error('Environment validation failed:', error);
    throw error;
  }
};

/**
 * Get validated environment configuration
 * This is the main export that should be used throughout the application
 */
export const env = validateEnvironment();

/**
 * Check if all required environment variables are set for production
 */
export const checkProductionReadiness = (): { ready: boolean; issues: string[] } => {
  const issues: string[] = [];

  try {
    const config = validateEnvironment();
    
    if (config.NODE_ENV !== 'production') {
      return { ready: true, issues: [] }; // Only check production readiness in production
    }

    // Check for default/weak values in production
    if (config.JWT_SECRET.includes('dev') || config.JWT_SECRET.length < 32) {
      issues.push('JWT_SECRET is not production-ready');
    }

    if (config.ADMIN_PASSWORD.includes('Dev') || config.ADMIN_PASSWORD === 'Admin123!') {
      issues.push('ADMIN_PASSWORD is not production-ready');
    }

    if (config.REDIS_PASSWORD.includes('dev') || config.REDIS_PASSWORD.length < 8) {
      issues.push('REDIS_PASSWORD is not production-ready');
    }

    if (!config.DATABASE_SSL) {
      issues.push('DATABASE_SSL should be enabled in production');
    }

    if (config.TON_NETWORK !== 'mainnet') {
      issues.push('TON_NETWORK should be "mainnet" in production');
    }

    return {
      ready: issues.length === 0,
      issues,
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    issues.push(`Environment validation failed: ${errorMessage}`);
    return { ready: false, issues };
  }
};
