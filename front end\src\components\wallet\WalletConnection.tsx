'use client';

import { useState } from 'react';
import { Wallet, RefreshCw, LogOut, Copy, ExternalLink } from 'lucide-react';
import { useTonConnect } from '@/contexts/TonConnectContext';
import { toast } from 'react-hot-toast';

interface WalletConnectionProps {
  showBalance?: boolean;
  showDisconnect?: boolean;
  className?: string;
}

export default function WalletConnection({ 
  showBalance = true, 
  showDisconnect = true,
  className = '' 
}: WalletConnectionProps) {
  const { wallet, connected, connecting, connect, disconnect, refreshWallet } = useTonConnect();
  const [refreshing, setRefreshing] = useState(false);

  const handleConnect = async () => {
    try {
      await connect();
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (error) {
      console.error('Disconnection failed:', error);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await refreshWallet();
      toast.success('Wallet refreshed');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh wallet');
    } finally {
      setRefreshing(false);
    }
  };

  const copyAddress = () => {
    if (wallet?.address) {
      navigator.clipboard.writeText(wallet.address);
      toast.success('Address copied to clipboard');
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0';
    if (num < 0.001) return '<0.001';
    return num.toFixed(3);
  };

  const openInExplorer = () => {
    if (wallet?.address) {
      const explorerUrl = wallet.network === 'mainnet' 
        ? `https://tonviewer.com/${wallet.address}`
        : `https://testnet.tonviewer.com/${wallet.address}`;
      window.open(explorerUrl, '_blank');
    }
  };

  if (!connected) {
    return (
      <div className={`${className}`}>
        <button
          onClick={handleConnect}
          disabled={connecting}
          className="btn-primary inline-flex items-center"
        >
          {connecting ? (
            <>
              <div className="spinner-sm mr-2" />
              Connecting...
            </>
          ) : (
            <>
              <Wallet className="h-4 w-4 mr-2" />
              Connect Wallet
            </>
          )}
        </button>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <div className="h-8 w-8 bg-ton-100 rounded-full flex items-center justify-center mr-3">
              <Wallet className="h-4 w-4 text-ton-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">TON Wallet</p>
              <p className="text-xs text-gray-500 capitalize">{wallet?.network}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Refresh balance"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
            {showDisconnect && (
              <button
                onClick={handleDisconnect}
                className="p-1 text-gray-400 hover:text-red-600 rounded"
                title="Disconnect wallet"
              >
                <LogOut className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        <div className="space-y-2">
          {/* Address */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Address:</span>
            <div className="flex items-center space-x-1">
              <span className="text-sm font-mono text-gray-900">
                {formatAddress(wallet?.address || '')}
              </span>
              <button
                onClick={copyAddress}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Copy address"
              >
                <Copy className="h-3 w-3" />
              </button>
              <button
                onClick={openInExplorer}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="View in explorer"
              >
                <ExternalLink className="h-3 w-3" />
              </button>
            </div>
          </div>

          {/* Balance */}
          {showBalance && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Balance:</span>
              <span className="text-sm font-semibold text-gray-900">
                {formatBalance(wallet?.balance || '0')} TON
              </span>
            </div>
          )}
        </div>

        {/* Network indicator */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-center">
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              wallet?.network === 'mainnet' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-1 ${
                wallet?.network === 'mainnet' ? 'bg-green-400' : 'bg-yellow-400'
              }`} />
              {wallet?.network === 'mainnet' ? 'Mainnet' : 'Testnet'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
